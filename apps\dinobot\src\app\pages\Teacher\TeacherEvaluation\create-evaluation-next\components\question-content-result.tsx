import { Button } from '@dinobot/components-ui'
import { <PERSON>, Co<PERSON>, Keyboard, Sparkles, Trash, Trash2 } from 'lucide-react'
import React, { useReducer, useState } from 'react'
import { useEvaluationParamsStore } from '../../store/evaluation-params.store'
import { ControlQuestionMedia, ControlQuestionMediaPartial, ControlQuestionPartialWithRelations } from '@dinobot/prisma'
import EditorQuill from './editeur/editor'
import { useGenerateControlQuestions } from '../../hooks/useGenerateControlQuestions'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@dinobot/components-ui'
import { MemoizedReactMarkdown } from '@dinobot/components-ui'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'

import rehypemathjax from 'rehype-mathjax'
import { isEmptyOrNull } from '@dinobot/utils'
import { useTranslation } from 'react-i18next'
import { cn } from '@dinobot/utils'
import { getLangDir } from 'rtl-detect'
import MediaDialog from './media-dialog'
import MediaTooltip from './media-tooltip'
import { ImagePreviewModal } from './dialogs/components/image-preview-modal'
import { VideoPreviewModal } from './dialogs/components/video-preview-modal'
import { PlayCircle } from 'lucide-react'

type QuestionContentProps = {
    question: ControlQuestionPartialWithRelations
    quesNum: number
    exoIndex: number
    setIsEditeurOpen: (b: boolean) => void
    isEditeurOpen: boolean
}

const QuestionContentResult = ({
    exoIndex,
    question,
    quesNum,
    setIsEditeurOpen,
    isEditeurOpen
}: QuestionContentProps) => {
    const { t, i18n } = useTranslation('teacher/myClass/evaluation/next/exo/question')
    const dir = getLangDir(i18n.language)
    const {
        removeQuestion,
        chapter,
        updateQuestion,
        evalProps,
        setMediaCount,
        mediaCount
    } = useEvaluationParamsStore()

    const generateQuestionsMutation = useGenerateControlQuestions()
    const [questionMedias, setQuestionMedias] = useState<
        ControlQuestionMediaPartial[]
    >(question.medias || [])

    // State for modals
    const [isPreviewOpen, setIsPreviewOpen] = useState(false)
    const [previewMedia, setPreviewMedia] = useState<{
        url: string
        name: string
        type: 'image' | 'video'
    } | null>(null)

    const initStateQuestion = {
        qstCortexIsOpen: false,
        solCortexIsOpen: false,
        isGeneratingQuestion: false,
        isGeneratingSolution: false,
        copied: false,
        copiedSol: false,
        isEditQuestion: false,
        isEditSolution: false,
        notation: '1',
        type: t('quizz'),
        isAIupdate: false
    }
    const [
        {
            isGeneratingQuestion,
            isGeneratingSolution,
            qstCortexIsOpen,
            solCortexIsOpen,
            copied,
            copiedSol,
            isEditQuestion,
            isEditSolution,
            notation,
            type,
            isAIupdate
        },
        dispatch
    ] = useReducer(
        (
            state: typeof initStateQuestion,
            change: Partial<typeof initStateQuestion>
        ) => ({ ...state, ...change }),
        initStateQuestion
    )
    const typesQuestion = [
        t('quizz'),
        t('question_de_connaissances'),
        t('question_calculatoire'),
        t('question_d_analyses'),
        t('question_synthese'),
        t('question_reflexion_critique')
    ]

    const onCopy = (text: string, name: 'copied' | 'copiedSol') => {
        dispatch({ [name]: true })
        navigator.clipboard.writeText(text)
        setTimeout(() => dispatch({ [name]: false }), 1000)
    }
    const updateNotation = (notation: string) => {
        updateQuestion(exoIndex, quesNum, { ...question, score: notation })
        dispatch({ notation })
    }
    const updateType = (type: string) => {
        updateQuestion(exoIndex, quesNum, { ...question, type })
        dispatch({ type })
    }
    const generateWithAi = async (
        domain: string,
        level: string,
        chapter: string,
        type: 'question' | 'solution'
    ) => {
        try {
            dispatch({ isAIupdate: true })
            // Set loading state based on generation type
            if (type === 'question') dispatch({ isGeneratingQuestion: true })
            else dispatch({ isGeneratingSolution: true })

            // Prepare generator input
            const generatorInput = {
                questionsContentsList: [question.content!],
                questionsTitlesList: [''],
                solutionsContentsList: [question.solution!],
                domain,
                level,
                chapter,
                questionsNumber: 1,
                execisesNumber: 1,
                generateSolutionOnly: type === 'solution' // New flag
            }

            const result = await generateQuestionsMutation.mutateAsync(generatorInput)
            if (!result) return

            const generatedContent = result[0][0]

            // Prepare update data based on generation type
            const updateData =
                type === 'question'
                    ? {
                          content: generatedContent.questionContent,
                          solution: generatedContent.solution
                      }
                    : {
                          solution: generatedContent.solution
                      }

            // Update store
            updateQuestion(exoIndex, quesNum, { ...question, ...updateData })
        } finally {
            // Reset loading states
            dispatch({
                isGeneratingQuestion: false,
                isGeneratingSolution: false
            })
        }
    }
    const handleOpenPreview = (
        url: string,
        name: string,
        type: 'image' | 'video'
    ) => {
        setPreviewMedia({ url, name, type })
        setIsPreviewOpen(true)
    }

    const handleClosePreview = () => {
        setIsPreviewOpen(false)
        setPreviewMedia(null)
    }

    return (
        <div className="mt-2 relative">
            <ImagePreviewModal
                isOpen={isPreviewOpen && previewMedia?.type === 'image'}
                onClose={handleClosePreview}
                imageUrl={previewMedia?.url}
                imageName={previewMedia?.name}
            />
            <VideoPreviewModal
                isOpen={isPreviewOpen && previewMedia?.type === 'video'}
                onClose={handleClosePreview}
                videoUrl={previewMedia?.url}
                videoName={previewMedia?.name}
            />
            <Button
                variant="outline"
                className={cn(
                    `text-dinoBotRed hover:text-dinoBotRed/70 absolute top-9 px-1 border-dinoBotRed bg-transparent`,
                    dir === 'rtl' ? 'left-2' : 'right-2'
                )}
                onClick={() => removeQuestion(exoIndex, quesNum)}
            >
                <Trash2 />
            </Button>
            <h4
                className={cn(
                    'text-dinoBotBlue mt-1',
                    dir === 'rtl' && 'text-end'
                )}
            >
                {t('title')} {quesNum + 1}
            </h4>
            <div className="flex flex-col gap-2 rounded-lg bg-dinoBotLightGray/50 border-dinoBotLightGray border p-4">
                <div
                    className={cn(
                        'flex gap-4 py-2',
                        dir === 'rtl' && 'flex-row-reverse'
                    )}
                    onClick={() =>
                        dispatch({
                            isEditQuestion: false,
                            isEditSolution: false
                        })
                    }
                >
                    <div className="flex gap-2 items-center">
                        <span className="text-nowrap">{t('type')}</span>
                        <Select value={type} onValueChange={updateType}>
                            <SelectTrigger className="bg-dinoBotWhite min-w-52">
                                <SelectValue
                                    placeholder={t('type_placeholder')}
                                    className="bg-dinoBotWhite"
                                />
                            </SelectTrigger>
                            <SelectContent className="">
                                {typesQuestion.map(size => (
                                    <SelectItem
                                        key={size}
                                        value={size.toString()}
                                    >
                                        {size}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="flex gap-2 items-center">
                        <span className="text-nowrap">{t('notation')}</span>
                        <Select value={notation} onValueChange={updateNotation}>
                            <SelectTrigger className="w-20 bg-dinoBotWhite">
                                <SelectValue
                                    placeholder="/"
                                    className="bg-dinoBotWhite"
                                />
                            </SelectTrigger>
                            <SelectContent className="w-20">
                                {Array.from(
                                    { length: Number(evalProps?.letterRange) },
                                    (_, i) => (i + 1) * 1
                                ).map(size => (
                                    <SelectItem
                                        key={size}
                                        value={size.toString()}
                                    >
                                        {size}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>
                <div className={cn('flex flex-col gap-1')}>
                    <h3
                        className={cn(
                            'flex gap-2',
                            dir === 'rtl' && 'flex-row-reverse'
                        )}
                    >
                        {t('question_title')}
                    </h3>
                    <div
                        className={cn(
                            'flex gap-2',
                            dir === 'rtl' && 'flex-row-reverse'
                        )}
                    >
                        {isEditQuestion ? (
                            <EditorQuill
                                value={question.content!}
                                isAIupdate={isAIupdate}
                                theme={'snow'}
                                placeholder={t('placeholder')}
                                onChange={(value, text) => {
                                    dispatch({ isAIupdate: false })
                                    updateQuestion(exoIndex, quesNum, {
                                        ...question,
                                        content: text
                                    })
                                }}
                                onFocuseOut={() =>
                                    dispatch({ isEditQuestion: false })
                                }
                                cortexOpen={qstCortexIsOpen}
                                onCortexClose={() => {
                                    dispatch({ qstCortexIsOpen: false })
                                    setIsEditeurOpen(false)
                                }}
                                className={`chat size-full resize-none focus-within:outline-none sm:text-sm bg-dinoBotWhite rounded-lg border border-dinoBotGray/60`}
                            />
                        ) : (
                            <div
                                className="px-3 py-4 size-full resize-none focus-within:outline-none sm:text-sm bg-dinoBotWhite rounded-lg border border-dinoBotGray/60"
                                onClick={() =>
                                    dispatch({ isEditQuestion: true })
                                }
                            >
                                <MemoizedReactMarkdown
                                    remarkPlugins={[remarkGfm, remarkMath]}
                                    rehypePlugins={[rehypeRaw, rehypemathjax]}
                                    components={{
                                        p({ children }) {
                                            return <p dir={dir}>{children}</p>
                                        }
                                    }}
                                >
                                    {isEmptyOrNull(question.content)
                                        ? t('placeholder')
                                        : question.content}
                                </MemoizedReactMarkdown>
                            </div>
                        )}
                        <div className="flex flex-col justify-center">
                            <div
                                className={cn(
                                    'flex gap-2',
                                    dir === 'rtl' && 'flex-row-reverse'
                                )}
                            >
                                <Button
                                    onClick={() =>
                                        onCopy(question.content!, 'copied')
                                    }
                                    variant={'ghost'}
                                    className="p-0 flex justify-center items-center size-8"
                                >
                                    {copied ? (
                                        <Check className="text-dinoBotCyan" />
                                    ) : (
                                        <Copy className="cursor-pointer text-dinoBotSky" />
                                    )}
                                </Button>
                                <Button
                                    disabled={
                                        solCortexIsOpen ||
                                        isEditeurOpen ||
                                        !isEditQuestion
                                    }
                                    onClick={() => {
                                        dispatch({ qstCortexIsOpen: true })
                                        setIsEditeurOpen(true)
                                    }}
                                    variant={'ghost'}
                                    className="p-0 flex justify-center items-center size-8 text-dinoBotCyan hover:text-dinoBotCyan hover:bg-dinoBotCyan/10"
                                >
                                    <Keyboard />
                                </Button>
                            </div>
                            <Button
                                variant={'link'}
                                className="flex gap-1 px-0 text-dinoBotVibrantBlue justify-start w-full"
                                onClick={() =>
                                    generateWithAi(
                                        chapter?.domain?.name || '',
                                        chapter?.level?.name || '',
                                        chapter?.title || '',
                                        'question'
                                    )
                                }
                                disabled={isGeneratingQuestion}
                            >
                                {isGeneratingQuestion ? (
                                    <div className="animate-spin">⏳</div>
                                ) : (
                                    <Sparkles size={16} />
                                )}
                                <p>{t('generate')}</p>
                            </Button>
                        </div>
                    </div>
                </div>
                <div className="flex flex-row gap-1">
                    {questionMedias?.length > 0 && (
                        <div className="flex flex-col gap-1">
                            <div className="flex flex-wrap gap-2">
                                {questionMedias.map((media, index) => (
                                    <div
                                        key={index}
                                        className="relative rounded-md overflow-hidden bg-dinoBotRed border w-32 h-20 cursor-pointer"
                                        onClick={() =>
                                            handleOpenPreview(
                                                media.fileUrl!,
                                                media.fileName || 'media',
                                                media.fileType?.startsWith(
                                                    'image'
                                                )
                                                    ? 'image'
                                                    : 'video'
                                            )
                                        }
                                    >
                                        {media.fileType?.startsWith('image') ? (
                                            <img
                                                src={media.fileUrl!}
                                                alt={media.fileName || 'media'}
                                                className="object-cover w-full h-full"
                                            />
                                        ) : (
                                            <div className="w-full h-full bg-black flex items-center justify-center">
                                                <video
                                                    src={media.fileUrl!}
                                                    muted={true}
                                                    className="object-cover w-full h-full pointer-events-none"
                                                />
                                                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                                                    <PlayCircle className="text-white h-8 w-8" />
                                                </div>
                                            </div>
                                        )}
                                        <Button
                                            onClick={e => {
                                                e.stopPropagation() // Prevent modal from opening
                                                const newMedias =
                                                    questionMedias.filter(
                                                        (_, i) => i !== index
                                                    )
                                                setQuestionMedias(newMedias)
                                                updateQuestion(
                                                    exoIndex,
                                                    quesNum,
                                                    {
                                                        ...question,
                                                        medias: newMedias
                                                    }
                                                )
                                                setMediaCount(mediaCount - 1)
                                            }}
                                            variant="ghost"
                                            className="absolute bottom-1 left-1 p-0 flex justify-center items-center size-8 bg-dinoBotWhite border border-dinoBotRed"
                                        >
                                            <Trash className="cursor-pointer text-dinoBotRed" />
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                    <div
                        className={`flex gap-2  ${questionMedias?.length > 0 ? 'flex-col justify-between' : ''} `}
                    >
                        <MediaDialog
                            quesNum={quesNum}
                            exoIndex={exoIndex}
                            question={question}
                            questionMedias={
                                questionMedias as ControlQuestionMedia[]
                            }
                            setQuestionMedias={
                                setQuestionMedias as React.Dispatch<
                                    React.SetStateAction<ControlQuestionMedia[]>
                                >
                            }
                            dialogType="question"
                        />
                        <MediaTooltip />
                    </div>
                </div>

                <div className="flex flex-col gap-1">
                    <h3
                        className={cn(
                            'flex gap-2',
                            dir === 'rtl' && 'flex-row-reverse'
                        )}
                    >
                        {t('solution')}
                    </h3>
                    <div
                        className={cn(
                            'flex gap-2',
                            dir === 'rtl' && 'flex-row-reverse'
                        )}
                    >
                        {isEditSolution ? (
                            <EditorQuill
                                value={question.solution!}
                                isAIupdate={isAIupdate}
                                theme={'snow'}
                                placeholder={t('placeholder')}
                                onChange={(value, text) => {
                                    dispatch({ isAIupdate: false })
                                    updateQuestion(exoIndex, quesNum, {
                                        ...question,
                                        solution: text
                                    })
                                }}
                                onFocuseOut={() =>
                                    dispatch({ isEditSolution: false })
                                }
                                cortexOpen={solCortexIsOpen}
                                onCortexClose={() => {
                                    dispatch({ solCortexIsOpen: false })
                                    setIsEditeurOpen(false)
                                }}
                                className={`chat size-full resize-none focus-within:outline-none sm:text-sm bg-dinoBotWhite rounded-lg border border-dinoBotGray/60 `}
                            />
                        ) : (
                            <div
                                className="px-3 py-4 size-full resize-none focus-within:outline-none sm:text-sm bg-dinoBotWhite rounded-lg border border-dinoBotGray/60"
                                onClick={() =>
                                    dispatch({ isEditSolution: true })
                                }
                            >
                                <MemoizedReactMarkdown
                                    remarkPlugins={[remarkGfm, remarkMath]}
                                    rehypePlugins={[rehypeRaw, rehypemathjax]}
                                    components={{
                                        p({ children }) {
                                            return <p dir={dir}>{children}</p>
                                        }
                                    }}
                                >
                                    {isEmptyOrNull(question.solution)
                                        ? t('placeholder')
                                        : question.solution}
                                </MemoizedReactMarkdown>
                            </div>
                        )}
                        <div className="flex flex-col justify-center">
                            <div
                                className={cn(
                                    'flex gap-2',
                                    dir === 'rtl' && 'flex-row-reverse'
                                )}
                            >
                                <Button
                                    onClick={() =>
                                        onCopy(question.solution!, 'copiedSol')
                                    }
                                    variant={'ghost'}
                                    className="p-0 flex justify-center items-center size-8"
                                >
                                    {copiedSol ? (
                                        <Check className="text-dinoBotCyan" />
                                    ) : (
                                        <Copy className="cursor-pointer text-dinoBotSky" />
                                    )}
                                </Button>
                                <Button
                                    disabled={
                                        qstCortexIsOpen ||
                                        isEditeurOpen ||
                                        !isEditSolution
                                    }
                                    onClick={() => {
                                        dispatch({ solCortexIsOpen: true })
                                        setIsEditeurOpen(true)
                                    }}
                                    variant={'ghost'}
                                    className="p-0 flex justify-center items-center size-8 text-dinoBotCyan hover:text-dinoBotCyan hover:bg-dinoBotCyan/10"
                                >
                                    <Keyboard />
                                </Button>
                            </div>
                            <Button
                                variant={'link'}
                                className="flex gap-1 px-0 text-dinoBotVibrantBlue justify-start w-full"
                                onClick={() =>
                                    generateWithAi(
                                        chapter?.domain?.name || '',
                                        chapter?.level?.name || '',
                                        chapter?.title || '',
                                        'solution'
                                    )
                                }
                                disabled={isGeneratingSolution}
                            >
                                {isGeneratingSolution ? (
                                    <div className="animate-spin">⏳</div>
                                ) : (
                                    <Sparkles size={16} />
                                )}
                                <p>{t('generate')}</p>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default QuestionContentResult

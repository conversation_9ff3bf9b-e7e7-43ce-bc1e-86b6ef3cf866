import {
    Calendar,
    momentLocalizer,
    Navigate,
    NavigateAction,
    View
} from 'react-big-calendar'
import React, { useCallback, useEffect, useState } from 'react'
import { getLangDir } from 'rtl-detect'
import 'moment/locale/fr'
import 'moment/locale/ar'
import 'moment/locale/en-gb'
import 'react-big-calendar/lib/css/react-big-calendar.css'
import moment from 'moment'
import { useTranslation } from 'react-i18next'
import HeaderCalendar from './header-calendar'
import { useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { useResizeObserver } from '@dinobot/hooks'
import { PlannedEvaluationPartial } from '@dinobot/prisma'
import { useAuthApiClient } from '../../../../../../contexts/AppContext'

const EditeurCalendar = () => {
    const [size, elementRef] = useResizeObserver<HTMLDivElement>()
    const { i18n } = useTranslation()
    const [currentDate, setCurrentDate] = useState(new Date())
    const direction = getLangDir(i18n.language)
    moment.locale(i18n.language)
    const { courseId } = useParams<{ courseId: string }>()
    const localizer = momentLocalizer(moment)
    const [myEvents, setMyEvents] = useState<
        (PlannedEvaluationPartial & { color: string })[]
    >([])

    const handleNavigate = (
        newDate: Date,
        view: View,
        action: NavigateAction
    ) => {
        let newCurrentDate = currentDate
        switch (action) {
            case Navigate.PREVIOUS:
                newCurrentDate = moment(currentDate)
                    .subtract(1, 'week')
                    .toDate()
                break
            case Navigate.NEXT:
                newCurrentDate = moment(currentDate).add(1, 'week').toDate()
                break
            case Navigate.TODAY:
                newCurrentDate = new Date()
                break
            case Navigate.DATE:
                newCurrentDate = newDate
                break
            default:
                break
        }
        setCurrentDate(newCurrentDate)
    }
    const setEvents = useCallback((events: PlannedEvaluationPartial[]) => {
        const evts = events.map(e => ({ ...e, color: '#FF5E0E' }))
        setMyEvents(evts)
    }, [])
    const apiClient = useAuthApiClient()

    const { data: events = [] } = useQuery({
        queryKey: ['calendar-events', courseId],
        queryFn: async () => {
            const response = await apiClient.get('/api/events', {
                params: {
                    classId: courseId,
                    // Add other filters as needed
                }
            })
            return response.data
        },
        enabled: !!courseId
    })

    useEffect(() => {
        if (events.length > 0) {
            setEvents(events as PlannedEvaluationPartial[])
        }
    }, [events, setEvents])
    const eventStyleGetter = (event: { color: string }) => {
        const backgroundColor = event.color || '#3174ad' // Définit la couleur par défaut si aucune couleur n'est spécifiée
        const style = {
            backgroundColor,
            borderRadius: '5px',
            opacity: 0.8,
            color: 'white',
            border: '0px',
            display: 'block'
        }
        return {
            style
        }
    }
    useEffect(() => console.log(size), [size])
    return (
        <div ref={elementRef} className="h-full">
            <Calendar
                localizer={localizer}
                events={myEvents}
                startAccessor="availableDate"
                endAccessor="dueDate"
                style={{ height: size.height - 20 }}
                defaultView="week"
                views={['month', 'week', 'day']}
                selectable={true}
                components={{ toolbar: HeaderCalendar }}
                onNavigate={handleNavigate}
                date={currentDate}
                rtl={direction === 'rtl'}
                eventPropGetter={eventStyleGetter}
            />
        </div>
    )
}

export default EditeurCalendar

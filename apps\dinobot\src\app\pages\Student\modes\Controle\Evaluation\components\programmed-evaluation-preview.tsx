import React, { useEffect, useRef, useState } from 'react'
import moment from 'moment'
import Loading from '../../components/loading'
import { DesmosUi, MemoizedReactMarkdown, Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@dinobot/components-ui'
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@dinobot/components-ui'
import { Button } from '@dinobot/components-ui'
import { Check, Clipboard, Printer } from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { toast } from 'sonner'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'
import rehypemathjax from 'rehype-mathjax'
import { z } from 'zod'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { useAuthApiClient } from '../../../../../../contexts/AppContext'
import AssetsViewer from './assets-viewer/assets-viewer'
import { ControlQuestionMedia, ControlWithPartialRelations, ControlWithPartialRelationsSchema, PlannedEvaluationWithPartialRelationsSchema } from '@dinobot/prisma'
import { ControlFeedbackOutput } from '@dinobot/utils'
import { useAccountStore ,useCtrlModeStore} from '@dinobot/stores'

type ProgrammedEvaluationPreviewProps = {
    plannedEvaluation: z.infer<
        typeof PlannedEvaluationWithPartialRelationsSchema
    >
    control?: ControlWithPartialRelations
    availableDate?: Date | null
}

type FontStyle = {
    fontFamily?: string
    fontSize?: string | number
}

const getFontStyle = (
    plannedEvaluation: z.infer<
        typeof PlannedEvaluationWithPartialRelationsSchema
    >
): FontStyle => {
    return {
        fontFamily: plannedEvaluation?.control?.fontName || undefined,
        fontSize: plannedEvaluation?.control?.fontSize || undefined
    }
}

// This is the left side of the control mode
function transformControleData(
    data: z.infer<typeof ControlWithPartialRelationsSchema>
): ControlFeedbackOutput | null {
    if (!data) {
        return null
    }
    const transformedData: ControlFeedbackOutput = {
        score: 0,
        scoreExplanation: '',
        exercises: data?.exercises?.map((item, i) => ({
            id: item.id,
            title: 'exercice: ' + (i + 1),
            score: 0,
            medias: item.medias,
            hasStatment: item.hasStatment,
            statement: item.statement,
            questions: item?.questions?.map(q => {
                return {
                    id: q.id,
                    questionContent: q.content,
                    answer: undefined,
                    feedback: q.solution,
                    desmosCode: q?.desmosCode,
                    contentType: q?.type,
                    medias: q?.medias
                }
            })
        })) as any
    }
    return transformedData
}

const ProgrammedEvaluationPreview = ({
    control,
    availableDate,
    plannedEvaluation
}: ProgrammedEvaluationPreviewProps) => {
    const { t, i18n } = useTranslation('app/mode/controle/controlPreview')
    const dir = getLangDir(i18n.language)
    const fontStyle = getFontStyle(plannedEvaluation)
    const {
        subject,
        ctrlInfo,
        setControle,
        controle,
        timeStart,
        isRunning,
        isLoading,
        setLoading: setLoding,
        reset,
        isNoLimit
    } = useCtrlModeStore()
    const { evaluationId } = useParams<{ evaluationId: string }>()
    const apiClient = useAuthApiClient()
    const { data: answersStudent } = useQuery({
        queryKey: ['commentProfEval', evaluationId],
        queryFn: async () => {
            if (!evaluationId) return null
            const response = await apiClient.get(`/api/control-mode/evaluation/${evaluationId}/comments`)
            return response.data
        },
        select: data => data?.answers,
        enabled: !!evaluationId && !!apiClient
    })

    const ctrlRef = useRef<HTMLDivElement>(null)
    const { user } = useAccountStore()

    const getControlQuestions = async () => {
        if (!ctrlInfo.chapterId || !subject) {
            reset()
        }
        setLoding(true)

        if (control != null) {
            const ctrl = transformControleData(control!)
            setControle(ctrl)
        } else {
            toast.error(t('errorOccurred'))
            reset()
        }
        setLoding(false)
    }

    useEffect(() => {
        try {
            const now = new Date()
            if (now < availableDate!) {
                setLoding(true)
                return
            }
            timeStart()
            getControlQuestions()
        } catch (error) {
            toast.error(t('error'))
            reset()
            console.error(error)
        }
    }, [])

    const copyControl = () => {
        let text = ''
        controle?.exercises.forEach(exo => {
            text += `${exo.title}\n`
            exo.questions.forEach((question, index) => {
                text += ` ${index + 1}. ${question.questionContent}\n`
            })
        })
        navigator.clipboard.writeText(text)
    }
    const printCtrl = useReactToPrint({
        contentRef: ctrlRef
    })
    const isShowCorrection =
        isRunning || isLoading || isNoLimit || !plannedEvaluation.isCorrected
    return (
        <Tabs
            defaultValue="sujetducontrole"
            className="m-2 mx-8 xl:h-[70vh] relative"
        >
            <TabsList
                className={`flex justify-start ${dir === 'rtl' ? 'flex-row-reverse' : ''} p-0`}
            >
                <TabsTrigger
                    value="sujetducontrole"
                    className="w-44 border-dinoBotBlue border -mb-2 border-b-dinoBotBlue rounded-b-none underline text-dinoBotBlue data-[state=active]:bg-dinoBotBlue data-[state=active]:text-white"
                >
                    {t('controlSubject')}
                </TabsTrigger>
                <TabsTrigger
                    value="macopie"
                    className={`w-44 border-dinoBotBlue border -mb-2 border-b-dinoBotBlue rounded-b-none underline text-dinoBotBlue data-[state=active]:bg-dinoBotBlue data-[state=active]:text-white`}
                >
                    {t('myCopy')}
                </TabsTrigger>
                <TabsTrigger
                    value="correction"
                    className={`w-44 border-dinoBotBlue border -mb-2 border-b-dinoBotBlue rounded-b-none underline text-dinoBotBlue data-[state=active]:bg-dinoBotBlue data-[state=active]:text-white`}
                    disabled={isShowCorrection}
                >
                    {t('correction')}
                </TabsTrigger>
            </TabsList>
            <TabsContent
                value="sujetducontrole"
                className="m-0 p-4 xl:overflow-y-scroll h-full border border-dinoBotBlue "
                style={fontStyle}
            >
                <div ref={ctrlRef}>
                    {isLoading ? (
                        <Loading />
                    ) : (
                        controle?.exercises.map(answer => (
                            <div
                                key={answer.id}
                                className={`flex flex-col ${dir === 'ltr' ? '' : 'text-right'} `}
                            >
                                <h2
                                    className="text-lg font-bold text-sky-900 "
                                    style={fontStyle}
                                >
                                    {answer.title}
                                </h2>
                                {answer.hasStatment ? (
                                    <div className="bg-dinoBotVibrantBlue/5 p-1 rounded drop-shadow">
                                        <p className="italic">
                                            {answer.statement}
                                        </p>
                                        {answer.medias &&
                                        answer.medias.length > 0 ? (
                                            <div className="py-1.5">
                                                <AssetsViewer
                                                    attachments={
                                                        answer.medias.filter(
                                                            m =>
                                                                m.type ==
                                                                'statement'
                                                        ) as ControlQuestionMedia[]
                                                    }
                                                />
                                            </div>
                                        ) : null}
                                    </div>
                                ) : null}

                                <div className="flex flex-col m-2">
                                    {answer.questions.map((content, index) => (
                                        <div
                                            key={content.id}
                                            className="flex justify-between items-start gap-1"
                                        >
                                            <div className="flex flex-col my-1 min-w-96">
                                                <GenerateFormatedText
                                                    className="text-dinoBotGray font-semibold"
                                                    content={index + 1 + ')'}
                                                >
                                                    {index +
                                                        1 +
                                                        ')' +
                                                        content.questionContent}
                                                </GenerateFormatedText>
                                                {content.desmosCode &&
                                                content.desmosCode
                                                    .expressions ? (
                                                    <DesmosUi
                                                        data={
                                                            content.desmosCode
                                                        }
                                                        className="w-96 h-80"
                                                    />
                                                ) : (
                                                    ''
                                                )}
                                            </div>
                                            {content.medias &&
                                            content.medias.length > 0 ? (
                                                <div className="py-1.5x">
                                                    <AssetsViewer
                                                        attachments={
                                                            content.medias as ControlQuestionMedia[]
                                                        }
                                                    />
                                                </div>
                                            ) : null}
                                        </div>
                                    ))}
                                </div>
                                {answer.medias &&
                                answer.medias.filter(m => m.type == 'global')
                                    .length > 0 ? (
                                    <div className="py-1.5 bg-dinoBotLightBlue/20 shadow">
                                        <AssetsViewer
                                            attachments={
                                                answer.medias.filter(
                                                    m => m.type == 'global'
                                                ) as ControlQuestionMedia[]
                                            }
                                        />
                                    </div>
                                ) : null}
                            </div>
                        ))
                    )}
                </div>
                <div className="absolute top-[-9999px]">
                    <div ref={ctrlRef} className="p-8" style={fontStyle}>
                        <div className="w-full flex gap-2 items-center justify-center py-3 bg-dinoBotVibrantBlue/5">
                            <img
                                src='/dinobot-logo-chat.svg'
                                alt="logo"
                                width={48}
                                height={48}
                            />
                            <div className="text-2xl text-dinoBotBlue font-semibold">
                                DinoBot
                            </div>
                        </div>
                        {isLoading ? (
                            <Loading />
                        ) : (
                            controle?.exercises.map(answer => (
                                <div
                                    key={answer.id}
                                    className={`flex flex-col ${dir === 'ltr' ? '' : 'text-right'} `}
                                >
                                    <h2
                                        className="text-lg font-bold text-sky-900 "
                                        style={fontStyle}
                                    >
                                        {answer.title}
                                    </h2>
                                    {/* {answer.hasStatment ? ( */}
                                    <div>
                                        <div>
                                            <p>{answer.statement}</p>
                                        </div>
                                    </div>
                                    {/* ) : null} */}
                                    {answer.medias &&
                                    answer.medias.length > 0 ? (
                                        <div className="py-1.5">
                                            <AssetsViewer
                                                attachments={
                                                    answer.medias as ControlQuestionMedia[]
                                                }
                                            />
                                        </div>
                                    ) : null}
                                    <div className="flex flex-col m-2">
                                        {answer.questions.map(
                                            (content, index) => (
                                                <div
                                                    key={content.id}
                                                    className="flex flex-col my-1"
                                                >
                                                    <GenerateFormatedText
                                                        className="text-dinoBotGray font-semibold"
                                                        content={
                                                            index + 1 + ')'
                                                        }
                                                    >
                                                        {index +
                                                            1 +
                                                            ')' +
                                                            content.questionContent}
                                                    </GenerateFormatedText>
                                                    {content.desmosCode &&
                                                    content.desmosCode
                                                        .expressions ? (
                                                        <DesmosUi
                                                            data={
                                                                content.desmosCode
                                                            }
                                                            className="w-96 h-80"
                                                            style={{
                                                                pageBreakInside:
                                                                    'avoid'
                                                            }}
                                                        />
                                                    ) : (
                                                        ''
                                                    )}
                                                    {content.medias &&
                                                    content.medias.length >
                                                        0 ? (
                                                        <div className="py-1.5">
                                                            <AssetsViewer
                                                                attachments={
                                                                    content.medias as ControlQuestionMedia[]
                                                                }
                                                            />
                                                        </div>
                                                    ) : null}
                                                </div>
                                            )
                                        )}
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
            </TabsContent>
            <CtrlOptions onCopy={copyControl} onPrint={printCtrl} />
            <TabsContent
                value="macopie"
                className="m-0 p-8 overflow-y-scroll h-full border border-dinoBotBlue"
                style={fontStyle}
            >
                <div
                    className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
                    ref={ctrlRef}
                >
                    <div className="flex justify-between">
                        <h3>
                            {user.firstName} {user.lastName}
                        </h3>
                        <h3>{moment().format('DD/MM/yyyy')}</h3>
                    </div>
                    <div className="flex flex-col items-center">
                        <h2 className="underline">
                            {dir === 'ltr'
                                ? t('controlOf') + ' ' + control?.domainName
                                : control?.domainName + ' ' + t('controlOf')}
                        </h2>
                    </div>
                    <div className="mt-5 ml-5">
                        {isLoading ? (
                            <Loading />
                        ) : (
                            controle?.exercises.map(answer => (
                                <div key={answer.id} className="flex flex-col ">
                                    <h2
                                        className="text-lg font-bold text-sky-900"
                                        style={fontStyle}
                                    >
                                        {answer.title}
                                    </h2>
                                    <div className="flex flex-col m-5">
                                        {answer.questions.map(
                                            (content, key) => (
                                                <div
                                                    key={content.id}
                                                    className="flex flex-col my-1"
                                                >
                                                    <GenerateFormatedText className="text-dinoBotGray font-semibold">
                                                        {key +
                                                            1 +
                                                            ')' +
                                                            content.questionContent}
                                                    </GenerateFormatedText>
                                                    {content.desmosCode &&
                                                    content.desmosCode
                                                        .expressions ? (
                                                        <DesmosUi
                                                            data={
                                                                content.desmosCode
                                                            }
                                                            className="w-96 h-80"
                                                        />
                                                    ) : (
                                                        ''
                                                    )}
                                                    {content.answer ? (
                                                        <MemoizedReactMarkdown
                                                            // className="text-dinoBotGray font-semibold mt-2 mb-4 text-left"
                                                            remarkPlugins={[
                                                                remarkGfm,
                                                                remarkMath
                                                            ]}
                                                            rehypePlugins={[
                                                                rehypeRaw,
                                                                rehypemathjax
                                                            ]}
                                                            components={{
                                                                p({
                                                                    children
                                                                }) {
                                                                    return (
                                                                        <p className="mb-2 last:mb-0">
                                                                            {
                                                                                children
                                                                            }
                                                                        </p>
                                                                    )
                                                                }
                                                            }}
                                                        >
                                                            {content.answer}
                                                        </MemoizedReactMarkdown>
                                                    ) : (
                                                        <div className="text-red-600 p-2 rounded-lg">
                                                            {t('noAnswer')}
                                                        </div>
                                                    )}
                                                    {!isShowCorrection && (
                                                        <GenerateFormatedText className="text-dinoBotBlackBlue p-2 rounded-lg">
                                                            {answersStudent?.find(
                                                                (as: any) =>
                                                                    as.controlQuestionId ===
                                                                    content.id
                                                            )?.feedback ?? ''}
                                                        </GenerateFormatedText>
                                                    )}
                                                </div>
                                            )
                                        )}
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
                <div className="absolute top-[-9999px]">
                    <div
                        className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} p-8`}
                        ref={ctrlRef}
                        style={fontStyle}
                    >
                        <div className="flex justify-between">
                            <h3>
                                {user.firstName} {user.lastName}
                            </h3>
                            <h3>{moment().format('DD/MM/yyyy')}</h3>
                        </div>
                        <div className="flex flex-col items-center">
                            <h2 className="underline">
                                {dir === 'ltr'
                                    ? t('controlOf') + ' ' + control?.domainName
                                    : control?.domainName +
                                      ' ' +
                                      t('controlOf')}
                            </h2>
                        </div>
                        <div className="mt-5 ml-5">
                            {isLoading ? (
                                <Loading />
                            ) : (
                                controle?.exercises.map(answer => (
                                    <div
                                        key={answer.id}
                                        className="flex flex-col "
                                    >
                                        <h2
                                            className="text-lg font-bold text-sky-900"
                                            style={fontStyle}
                                        >
                                            {answer.title}
                                        </h2>
                                        <div className="flex flex-col m-5">
                                            {answer.questions.map(
                                                (content, index) => (
                                                    <div
                                                        key={content.id}
                                                        className="flex flex-col my-1"
                                                    >
                                                        <GenerateFormatedText
                                                            className="text-dinoBotGray font-semibold"
                                                            content={
                                                                index + 1 + ')'
                                                            }
                                                        >
                                                            {index +
                                                                1 +
                                                                ')' +
                                                                content.questionContent}
                                                        </GenerateFormatedText>
                                                        {content.desmosCode &&
                                                        content.desmosCode
                                                            .expressions ? (
                                                            <DesmosUi
                                                                data={
                                                                    content.desmosCode
                                                                }
                                                                className="w-96 h-80"
                                                                style={{
                                                                    pageBreakInside:
                                                                        'avoid'
                                                                }}
                                                            />
                                                        ) : (
                                                            ''
                                                        )}
                                                        {content.answer ? (
                                                            <MemoizedReactMarkdown
                                                                // className="text-dinoBotGray font-semibold mt-2 mb-4 text-left"
                                                                remarkPlugins={[
                                                                    remarkGfm,
                                                                    remarkMath
                                                                ]}
                                                                rehypePlugins={[
                                                                    rehypeRaw,
                                                                    rehypemathjax
                                                                ]}
                                                                components={{
                                                                    p({
                                                                        children
                                                                    }) {
                                                                        return (
                                                                            <p className="mb-2 last:mb-0">
                                                                                {
                                                                                    children
                                                                                }
                                                                            </p>
                                                                        )
                                                                    }
                                                                }}
                                                            >
                                                                {content.answer}
                                                            </MemoizedReactMarkdown>
                                                        ) : (
                                                            <div className="text-red-600 p-2 rounded-lg">
                                                                {t('noAnswer')}
                                                            </div>
                                                        )}
                                                        {!isShowCorrection && (
                                                            <GenerateFormatedText className="text-dinoBotBlackBlue p-2 rounded-lg">
                                                                {answersStudent?.find(
                                                                    (as: any) =>
                                                                        as.controlQuestionId ===
                                                                        content.id
                                                                )?.feedback ??
                                                                    ''}
                                                            </GenerateFormatedText>
                                                        )}
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </div>
                </div>
            </TabsContent>
            <TabsContent
                value="correction"
                className="m-0 p-8 overflow-y-scroll h-full border border-dinoBotBlue"
                style={fontStyle}
            >
                <div
                    className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
                    ref={ctrlRef}
                >
                    <div className="flex justify-between">
                        <h3>
                            {user.firstName} {user.lastName}
                        </h3>
                        <h3>{moment().format('DD/MM/yyyy')}</h3>
                    </div>
                    <div className="flex flex-col items-center">
                        <h2 className="underline">
                            {dir === 'ltr'
                                ? t('controlOf') + ' ' + control?.domainName
                                : subject + ' ' + t('controlOf')}
                        </h2>
                    </div>
                    <div className="mt-5 ml-5">
                        {isLoading ? (
                            <Loading />
                        ) : (
                            controle?.exercises.map(answer => (
                                <div key={answer.id} className="flex flex-col ">
                                    <h2 className="text-lg font-bold text-sky-900">
                                        {answer.title}
                                    </h2>
                                    <div className="flex flex-col m-5">
                                        {answer.questions.map(
                                            (content, key) => (
                                                <div
                                                    key={content.id}
                                                    className="flex flex-col my-1"
                                                >
                                                    <GenerateFormatedText className="text-dinoBotGray font-semibold">
                                                        {key +
                                                            1 +
                                                            ')' +
                                                            content.questionContent}
                                                    </GenerateFormatedText>
                                                    {content.desmosCode &&
                                                    content.desmosCode
                                                        .expressions ? (
                                                        <DesmosUi
                                                            data={
                                                                content.desmosCode
                                                            }
                                                            className="w-96 h-80"
                                                        />
                                                    ) : (
                                                        ''
                                                    )}

                                                    {content.feedback && (
                                                        <GenerateFormatedText className="text-red-600 p-2 rounded-lg">
                                                            {content.feedback}
                                                        </GenerateFormatedText>
                                                    )}
                                                </div>
                                            )
                                        )}
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
                <div className="absolute top-[-9999px]">
                    <div
                        className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} p-8`}
                        ref={ctrlRef}
                        style={fontStyle}
                    >
                        <div className="flex justify-between">
                            <h3>
                                {user.firstName} {user.lastName}
                            </h3>
                            <h3>{moment().format('DD/MM/yyyy')}</h3>
                        </div>
                        <div className="flex flex-col items-center">
                            <h2 className="underline">
                                {dir === 'ltr'
                                    ? t('controlOf') + ' ' + subject
                                    : subject + ' ' + t('controlOf')}
                            </h2>
                        </div>
                        <div className="mt-5 ml-5">
                            {isLoading ? (
                                <Loading />
                            ) : (
                                controle?.exercises.map(answer => (
                                    <div
                                        key={answer.id}
                                        className="flex flex-col "
                                    >
                                        <h2
                                            className="text-lg font-bold text-sky-900"
                                            style={fontStyle}
                                        >
                                            {answer.title}
                                        </h2>
                                        <div className="flex flex-col m-5">
                                            {answer.questions.map(
                                                (content, index) => (
                                                    <div
                                                        key={content.id}
                                                        className="flex flex-col my-1"
                                                    >
                                                        <GenerateFormatedText
                                                            className="text-dinoBotGray font-semibold"
                                                            content={
                                                                index + 1 + ')'
                                                            }
                                                        >
                                                            {index +
                                                                1 +
                                                                ')' +
                                                                content.questionContent}
                                                        </GenerateFormatedText>
                                                        {content.desmosCode &&
                                                        content.desmosCode
                                                            .expressions ? (
                                                            <DesmosUi
                                                                data={
                                                                    content.desmosCode
                                                                }
                                                                className="w-96 h-80"
                                                                style={{
                                                                    pageBreakInside:
                                                                        'avoid'
                                                                }}
                                                            />
                                                        ) : (
                                                            ''
                                                        )}

                                                        {content.feedback && (
                                                            <GenerateFormatedText className="text-red-600 p-2 rounded-lg">
                                                                {
                                                                    content.feedback
                                                                }
                                                            </GenerateFormatedText>
                                                        )}
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </div>
                </div>
            </TabsContent>
        </Tabs>
    )
}

export default ProgrammedEvaluationPreview

const GenerateFormatedText = ({
    children,
    className,
    content = ''
}: {
    children: string
    className: string
    content?: string
}) => {
    return (
        <MemoizedReactMarkdown
            // className={cn(className)}
            remarkPlugins={[remarkGfm, remarkMath]}
            rehypePlugins={[rehypeRaw, rehypemathjax]}
            components={{
                p({ children }) {
                    return <p className="mb-2 last:mb-0">{children}</p>
                }
            }}
        >
            {`${content} ${children}`}
        </MemoizedReactMarkdown>
    )
}
interface CtrlOptionsProps {
    onCopy?: () => void
    onGenerate?: () => void
    onPrint?: () => void
    onDownload?: () => void
}

function CtrlOptions({ onCopy, onPrint }: CtrlOptionsProps) {
    const { t, i18n } = useTranslation('app/mode/controle/controlPreview')
    const [animGenerate, setAnimGenerate] = useState(false)
    const [animCopy, setAnimCopy] = useState(false)
    const [animDownload, setAnimDownload] = useState(false)

    const dir = getLangDir(i18n.language)
    useEffect(() => {
        if (animGenerate) setTimeout(() => setAnimGenerate(false), 1500)
        if (animCopy) setTimeout(() => setAnimCopy(false), 1500)
        if (animDownload) setTimeout(() => setAnimDownload(false), 1500)
    }, [animGenerate, animCopy, animDownload])

    return (
        <div
            className={`flex gap-2 absolute -bottom-16 ${dir === 'ltr' ? 'right-0' : 'left-0'} `}
        >
            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={
                            onCopy
                                ? () => {
                                      setAnimCopy(true)
                                      onCopy()
                                  }
                                : undefined
                        }
                    >
                        {!animCopy ? (
                            <Clipboard />
                        ) : (
                            <Check className="animate-fade-in" />
                        )}
                        <span className="sr-only">{t('copyExercise')}</span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('copyExercise')}
                </TooltipContent>
            </Tooltip>

            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={onPrint ?? undefined}
                    >
                        <Printer />
                        <span className="sr-only">
                            Imprimer L&apos;exercice
                        </span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('printExercise')}
                </TooltipContent>
            </Tooltip>
        </div>
    )
}

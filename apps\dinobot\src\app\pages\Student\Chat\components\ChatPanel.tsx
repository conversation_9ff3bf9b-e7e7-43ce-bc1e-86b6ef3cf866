import * as React from 'react'

import { PromptForm } from './prompt-form'
import Cookies from 'js-cookie';
import {useExamsStore} from '@dinobot/stores'
import { Dialog, DialogContent, DialogTitle, Button ,ButtonScrollToBottom, FooterText,/*ExamsPanel,*/ type messageFileType} from '@dinobot/components-ui'
import { type Session ,
    allemandExampleMessages,
    artsExampleMessages,
    chemistryExampleMessages,
    emcExampleMessages,
    enseignementScientifiqueExampleMessages,
    financeExampleMessages,
    frenchExampleMessages,
    geographyExampleMessages,
    hgGeoPolSciPolExampleMessages,
    histoireGeoExampleMessages,
    historyExampleMessages,
    humanitesLitteraturePhilosophieExampleMessages,
    informatiqueExampleMessages,
    italienExampleMessages,
    languesExampleMessages,
    lcleExampleMessages,
    mathApprofondiesExampleMessages,
    mathAppliquesExampleMessages,
    mathComplementairesExampleMessages,
    mathExpertesExampleMessages,
    mathsExampleMessages,
    microeconomieExampleMessages,
    nsiExampleMessages,
    otherExampleMessages,
    philosophieExampleMessages,
    physicsChemistryExampleMessages,
    physiqueExampleMessages,
    sciencesIngenieurExampleMessages,
    sciencesTechnologieExampleMessages,
    sesExampleMessages,
    sntExampleMessages,
    spanishExampleMessages,
    subjectQuestions,
    svtExampleMessages,
    technologieExampleMessages
} from '@dinobot/utils'
import { useTranslation } from 'react-i18next';
import { ChatRequestOptions, CreateMessage, Message, UIMessage } from 'ai'
import ExamsPanel from '@dinobot/components-ui/lib/exams-panel';

export interface ChatPanelProps {
    id?: string
    title?: string
    input: string
    setInput: (
        e:
            | React.ChangeEvent<HTMLInputElement>
            | React.ChangeEvent<HTMLTextAreaElement>
    ) => void
    imageFile: string
    status: 'submitted' | 'streaming' | 'ready' | 'error'
    setImageFile: (value: string) => void
    fileExtension: string
    setFileExtension: (value: string) => void
    isAtBottom: boolean
    scrollToBottom: () => void
    session?: Session
    messages: UIMessage[]
    addFileData: (data: messageFileType) => void
    handleSubmit: (
        event?: {
            preventDefault?: () => void
        },
        chatRequestOptions?: ChatRequestOptions
    ) => void
    append: (
        message: Message | CreateMessage,
        chatRequestOptions?: ChatRequestOptions
    ) => Promise<string | null | undefined>
}

export const ChatPanel = React.memo(function ChatPanel({
    input,
    setInput,
    imageFile,
    setImageFile,
    fileExtension,
    setFileExtension,
    isAtBottom,
    scrollToBottom,
    session,
    messages,
    status,
    handleSubmit,
    addFileData,
    append
}: ChatPanelProps) {
    const { isExamsPopupOpen, setopenExamsPopup, exercise } = useExamsStore()
    const { t } = useTranslation(['app/chat'],{keyPrefix:'exam'})
    const { t: tSubjects } = useTranslation(['lib/chat'])

    //console.log("session : " + JSON.stringify(session, null, 2))

    const topic = Cookies.get('topic')

    const feature = Cookies.get('feature')

    const restrictFreeUser =Cookies.get('restrictFreeUser')


    React.useEffect(() => {
        const feat = Cookies.get('feature')
        if (feat !== 'Chat' && feat !== 'Exam') {
            Cookies.set('feature', 'Chat')
            window.location.reload()
        }
    }, [])

    // Helper function to translate example messages
    function translateExampleMessages(
        messages: subjectQuestions,
        translator: (key: string) => string,
        namespace: string
    ): subjectQuestions {
        return messages.map(item => ({
            ...item,
            heading: translator(`${namespace}.${item.key}.heading`),
            subheading: translator(`${namespace}.${item.key}.subheading`),
            message: translator(`${namespace}.${item.key}.message`),
            animation: item.animation
        }))
    }

    // Define a type for the keys in the subjects map
    type SubjectKey =
        | 'mathématiques'
        | 'géographie'
        | 'français'
        | 'physique-chimie'
        | 'chimie'
        | 'svt'
        | 'emc'
        | 'histoire'
        | 'autre'
        | 'default'
        | 'physique'
        | 'histoire-géographie'
        | 'philosophie'
        | 'langues'
        | 'anglais'
        | 'ses'
        | 'microéconomie'
        | 'informatique'
        | 'nsi'
        | 'arts'
        | 'mathématiques complémentaires'
        | 'espagnol'
        | 'allemand'
        | 'italien'
        | 'sciences-ingenieur'
        | 'humanites-litterature-philosophie'
        | 'hggsp'
        | 'sciences-technologie'
        | 'lcle'
        | 'enseignement-scientifique'
        | 'sciences numériques et technologiques'
        | 'mathematiques-expertes'
        | 'mathematiques-approfondies'
        | 'mathematiques-appliquees'
        | 'finance'
        | 'technologie'
        | 'enseignement moral et civique'
        | 'sciences de la vie et de la terre'

    // Structure for subjects map
    const subjectsMap: Record<
        SubjectKey,
        { messages: subjectQuestions; namespace: string }
    > = {
        mathématiques: {
            messages: mathsExampleMessages,
            namespace: 'mathsExampleMessages'
        },
        'mathématiques complémentaires': {
            messages: mathComplementairesExampleMessages,
            namespace: 'mathComplementairesExampleMessages'
        },
        géographie: {
            messages: geographyExampleMessages,
            namespace: 'geographyExampleMessages'
        },
        français: {
            messages: frenchExampleMessages,
            namespace: 'frenchExampleMessages'
        },
        'physique-chimie': {
            messages: physicsChemistryExampleMessages,
            namespace: 'physicsChemistryExampleMessages'
        },
        physique: {
            messages: physiqueExampleMessages,
            namespace: 'physiqueExampleMessages'
        },
        chimie: {
            messages: chemistryExampleMessages,
            namespace: 'chemistryExampleMessages'
        },
        svt: { messages: svtExampleMessages, namespace: 'svtExampleMessages' },
        'sciences de la vie et de la terre': {
            messages: svtExampleMessages,
            namespace: 'svtExampleMessages'
        },
        emc: { messages: emcExampleMessages, namespace: 'emcExampleMessages' },
        'enseignement moral et civique': {
            messages: emcExampleMessages,
            namespace: 'emcExampleMessages'
        },
        histoire: {
            messages: historyExampleMessages,
            namespace: 'historyExampleMessages'
        },
        'histoire-géographie': {
            messages: histoireGeoExampleMessages,
            namespace: 'histoireGeoExampleMessages'
        },
        philosophie: {
            messages: philosophieExampleMessages,
            namespace: 'philosophieExampleMessages'
        },
        langues: {
            messages: languesExampleMessages,
            namespace: 'languesExampleMessages'
        },
        anglais: {
            messages: languesExampleMessages,
            namespace: 'languesExampleMessages'
        },
        espagnol: {
            messages: spanishExampleMessages,
            namespace: 'spanishExampleMessages'
        },
        allemand: {
            messages: allemandExampleMessages,
            namespace: 'allemandExampleMessages'
        },
        italien: {
            messages: italienExampleMessages,
            namespace: 'italienExampleMessages'
        },
        'sciences-ingenieur': {
            messages: sciencesIngenieurExampleMessages,
            namespace: 'sciencesIngenieurExampleMessages'
        },
        'humanites-litterature-philosophie': {
            messages: humanitesLitteraturePhilosophieExampleMessages,
            namespace: 'humanitesLitteraturePhilosophieExampleMessages'
        },
        hggsp: {
            messages: hgGeoPolSciPolExampleMessages,
            namespace: 'hgGeoPolSciPolExampleMessages'
        },
        'sciences-technologie': {
            messages: sciencesTechnologieExampleMessages,
            namespace: 'sciencesTechnologieExampleMessages'
        },
        lcle: {
            messages: lcleExampleMessages,
            namespace: 'lcleExampleMessages'
        },
        'enseignement-scientifique': {
            messages: enseignementScientifiqueExampleMessages,
            namespace: 'enseignementScientifiqueExampleMessages'
        },
        'sciences numériques et technologiques': {
            messages: sntExampleMessages,
            namespace: 'sntExampleMessages'
        },
        'mathematiques-expertes': {
            messages: mathExpertesExampleMessages,
            namespace: 'mathExpertesExampleMessages'
        },
        'mathematiques-approfondies': {
            messages: mathApprofondiesExampleMessages,
            namespace: 'mathApprofondiesExampleMessages'
        },
        'mathematiques-appliquees': {
            messages: mathAppliquesExampleMessages,
            namespace: 'mathAppliquesExampleMessages'
        },
        finance: {
            messages: financeExampleMessages,
            namespace: 'financeExampleMessages'
        },
        technologie: {
            messages: technologieExampleMessages,
            namespace: 'technologieExampleMessages'
        },
        ses: { messages: sesExampleMessages, namespace: 'sesExampleMessages' },
        microéconomie: {
            messages: microeconomieExampleMessages,
            namespace: 'microeconomieExampleMessages'
        },
        informatique: {
            messages: informatiqueExampleMessages,
            namespace: 'informatiqueExampleMessages'
        },
        nsi: { messages: nsiExampleMessages, namespace: 'nsiExampleMessages' },
        arts: {
            messages: artsExampleMessages,
            namespace: 'artsExampleMessages'
        },
        autre: {
            messages: otherExampleMessages,
            namespace: 'otherExampleMessages'
        },
        default: {
            messages: mathsExampleMessages,
            namespace: 'mathsExampleMessages'
        }
    }

    function getSubjectQuestions(subject: string): subjectQuestions {
        let subjKey: SubjectKey = 'autre'

        const myKeys = Object.keys(subjectsMap) as SubjectKey[]

        // if the subject exactly matches a key
        const lowerSubject = subject.toLowerCase()
        if (myKeys.includes(lowerSubject as SubjectKey)) {
            return translateExampleMessages(
                subjectsMap[lowerSubject as SubjectKey].messages,
                tSubjects,
                subjectsMap[lowerSubject as SubjectKey].namespace
            )
        }

        // split the subject into words and convert to lowercase
        const extractedWords = subject
            .split(' ')
            .map(item => item.toLowerCase())

        // filter the keys that match the extracted words
        const filteredKeys = myKeys.filter(key => extractedWords.includes(key))

        if (filteredKeys.length > 0) {
            subjKey = filteredKeys[0]
        }

        // translate example messages with the closest key
        return translateExampleMessages(
            subjectsMap[subjKey].messages,
            tSubjects,
            subjectsMap[subjKey].namespace
        )
    }

    return (
        <div className="fixed inset-x-0 bottom-0 w-full bg-gradient-to-b from-muted/30 from-0% to-muted/30 to-50% duration-300 ease-in-out animate-in dark:from-background/10 dark:from-10% dark:to-background/80">
            <ButtonScrollToBottom
                isAtBottom={isAtBottom}
                scrollToBottom={scrollToBottom}
            />

            <div
                className={`${exercise ? 'relative left-1/2' : 'mx-auto'} sm:max-w-2xl sm:px-4`}
            >
                {feature === 'Chat' ? (
                    <div className="mb-4 grid grid-cols-2 gap-2 px-4 sm:px-0">
                        {messages.length === 0 &&!exercise && getSubjectQuestions(topic || 'mathématiques').map(
                        (example, index) => (
                            <div
                                key={example.key}
                                className={`${!(!session?.user && restrictFreeUser === 'yes') ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed'} rounded-lg border bg-white p-4 hover:bg-zinc-50 dark:bg-zinc-950 dark:hover:bg-zinc-900  ${
                                    index > 1 && 'hidden 2xl:block'
                                } ${example.animation} `}
                                aria-disabled={
                                    !(
                                        !session?.user &&
                                        restrictFreeUser === 'yes'
                                    )
                                        ? false
                                        : true
                                }
                                onClick={async () => {
                                    if (
                                        !(
                                            !session?.user &&
                                            restrictFreeUser === 'yes'
                                        )
                                    ) {
                                        append({
                                            content: example.message,
                                            id: '',
                                            role: 'user'
                                        })
                                    }
                                }}
                            >
                                <div className="text-sm font-semibold">
                                    {example.heading}
                                </div>
                                <div className="text-sm text-zinc-600">
                                    {example.subheading}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : feature === 'Exam' && messages.length === 0 && !exercise && (
                    <div className="w-full flex flex-col justify-center items-center mb-6 md:mb-10">
                        <div className="text-xl px-10 text-dinoBotBlue font-bold mb-4 md:mb-6">
                            {t('title')}
                        </div>

                        <Button
                            onClick={() => setopenExamsPopup(false)}
                            variant="ghost"
                            className=" rounded-xl text-xl md:rounded-3xl text-dinoBotBlue md:text-4xl border border-dinoBotBlue hover:bg-dinoBotBlue hover:text-white hover:border p-4 md:p-10 transition-all duration-300"
                        >
                            {t('chooseExercise')}
                        </Button>
                    </div>
                )}
                <Dialog
                    open={isExamsPopupOpen}
                    onOpenChange={setopenExamsPopup}
                >
                    <DialogContent className="max-w-[800px] h-[600px] flex flex-col justify-start">
                        <div className="h-[100px]">
                            <DialogTitle className="text-dinoBotBlue text-2xl ml-4 mt-4 font-bold">
                                {t('dialog')}
                            </DialogTitle>
                        </div>
                        <ExamsPanel />
                    </DialogContent>
                </Dialog>

                <div className="mx-4 border-t rounded-md bg-background px-2 shadow-lg sm:rounded-t-xl sm:border md:pb-2 sm:mx-0">
                    <PromptForm
                        disabled={
                            !session?.user && restrictFreeUser === 'yes'
                                ? true
                                : false
                        }
                        input={input}
                        setInput={setInput}
                        imageFile={imageFile}
                        setImageFile={setImageFile}
                        fileExtension={fileExtension}
                        addFileData={addFileData}
                        status={status}
                        setFileExtension={setFileExtension}
                        HandleSubmit={handleSubmit}
                        messages={messages}
                    />
                    <FooterText className="hidden sm:block" />
                </div>
            </div>
        </div>
    )
})


import { useQuery } from '@tanstack/react-query'
import { Theme, ControlPartialWithRelations, PlannedEvaluationWithPartialRelations, ControlPartial } from '@dinobot/prisma'
import React from 'react'
import EvaluationCard from './evaluation-card'
import { useTranslation } from 'react-i18next'
import { useAuthApiClient } from '../../../../../../contexts/AppContext'
import { AccordionContent, AccordionItem, AccordionTrigger } from '@dinobot/components-ui'

type BlockProps = {
    theme: Theme
}

const ThemeBlock = ({ theme }: BlockProps) => {
    const apiClient = useAuthApiClient()
    const { t } = useTranslation(['app/courses/index'])

    const { data: plannedEvaluations, isLoading, error } = useQuery({
        queryKey: ['planned-controls', theme.id],
        queryFn: async (): Promise<PlannedEvaluationWithPartialRelations[]> => {
            const response = await apiClient.get(`/api/control-mode/planned/theme/${theme.id}`)
            return response.data
        }
    })

    const controls = plannedEvaluations?.map(item => ({
        ...item.control,
        plannedId: item.id,
        availableDate: item?.availableDate
    }))

    return (
        <AccordionItem
            value={theme.id}
            className="border-2 rounded-xl border-dinoBotGray/50 overflow-hidden"
        >
            <AccordionTrigger className="rounded-lg px-4 bg-dinoBotLightGray/50 text-dinoBotDarkGray">
                {theme.name}
            </AccordionTrigger>
            <AccordionContent className="px-4 py-2 flex gap-2 flex-wrap w-fit">
                {controls?.map((control, i) => (
                    <EvaluationCard
                        key={i}
                        evaluation={control as ControlPartial}
                        plannedId={control.plannedId}
                        availableDate={control.availableDate!}
                    />
                ))}
                {!controls?.length && !isLoading && (
                    <div className="text-dinoBotGray">
                        {t('accordion.not-found')}
                    </div>
                )}
                {isLoading && (
                    <div className="text-dinoBotGray">
                        Loading evaluations...
                    </div>
                )}
            </AccordionContent>
        </AccordionItem>
    )
}

export default ThemeBlock

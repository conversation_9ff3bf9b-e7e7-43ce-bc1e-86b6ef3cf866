//TODO - SERVER

import * as React from 'react'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue
} from '@dinobot/components-ui'
import Cookies from 'js-cookie'
import { selectUseTopicStore } from '@dinobot/stores'
import {useExoModeStore} from '@dinobot/stores'
import {useExamsStore} from '@dinobot/stores'
// import { getDomainsByLevel } from '@/lib/domain-level/actions'
import { toast } from 'sonner'
import {useAccountStore} from '@dinobot/stores'
import './select.css'
import { selectUseLocalStorageStore } from '@dinobot/stores'
import { getLangProps } from '@dinobot/utils'
import { User } from '@dinobot/prisma/lib/generated/zod/modelSchema/UserSchema';
import { useLocation, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Domain } from '@dinobot/prisma'

export const SelectTopic = ({ user: us }: { user: User }) => {
    const navigate = useNavigate()
    const { t , i18n} = useTranslation(['app/headers'],{keyPrefix:"topic"})
    const topic = Cookies.get('topic')
    const feature = Cookies.get('feature')
    const path = useLocation().pathname
    const setTopic = selectUseTopicStore.use.setTopic()
    const reset = useExoModeStore(state => state.reset)
    const { setopenExamsPopup, setExercise } = useExamsStore()
    const [domains, setDomains] = React.useState<Domain[]>([])
    const { setUser } = useAccountStore()
    const setDomainId = selectUseLocalStorageStore.use.setDomainId()
    const [oldLevelId, setOldLevelId] = React.useState<number | undefined>(us.levelId!)

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                setUser(us)
                // TODO setLeveDomain
                // const response = await getDomainsByLevel(us.levelId!)
                // if ('error' in response) throw new Error(response.error)
                // setDomains(response)
                if (!topic || oldLevelId !== us.levelId) {
                    // const topic = response[0]
                    // Cookies.set('topic', topic?.name)
                    // Cookies.set('topicId', topic?.id.toString())
                    // setDomainId(topic?.id)
                    // setTopic(topic)
                    setOldLevelId(us.levelId!)
                }
            } catch {
                toast.error('Erreur lors de la récupération des matières')
            }
        }
        fetchData()
    }, [us])
    React.useEffect(() => {
        //console.log("The actual topic stored in the cookies is " + topic)

        if (topic) {
            if (getDomain()) {
                Cookies.set('topic', getDomain()?.name ?? '')
                Cookies.set('topicId', getDomain()?.id?.toString() ?? '')
                setDomainId(getDomain()?.id||0)
                // setTopic(getDomain())
            }
        }
    }, [topic])
    const getDomain = () => {
        return domains.find(domain => domain.name === topic)
    }

    const handleTopicChange = (value: any) => {
        const domain = JSON.parse(value) as Domain

        try {
            Cookies.set('topic', domain.name)
            Cookies.set('topicId', domain.id.toString()!)
            setDomainId(domain.id)
            // setTopic(domain)
        } catch (err) {
            console.log('error: ' + err)
        } finally {
            switch (feature) {
                case 'Exo':
                    reset()
                    navigate('/exercise')
                    break
                case 'Exam':
                    setExercise(null)
                    navigate('/exam')
                    setopenExamsPopup(true)
                    break
                case 'Ctrl':
                    navigate('/create-controle')
                    break
                case 'Chat':
                    navigate('/')
                    break
                default:
                    navigate(path)
                    break
            }
            //setMessages([])
        }
    }
    const locale = i18n.language

    return (
        <Select
            value={JSON.stringify(getDomain()) as string}
            onValueChange={handleTopicChange}
        >
            <SelectTrigger
                className={`font-montserrat font-semibold  w-[40vw] md:w-[20vw] flex flex-row justify-center sm:text-base rounded-xl text-white border animate-fade-in-left transition-all duration-300 selected`}
                style={{ ['--color' as string]: getDomain()?.color }}
            >
                <SelectValue
                    placeholder={t('select')}
                    className="group/trigger *:stroke-white bg-red-500"
                />
            </SelectTrigger>
            <SelectContent className="rounded-2xl">
                <SelectGroup>
                    <SelectLabel>{t('module')}</SelectLabel>
                    {domains.map(domain => (
                        <SelectItem
                            key={domain.name}
                            className={`font-montserrat font-semibold rounded-2xl focus:text-white transition-all duration-300 select-color cursor-pointer`}
                            value={JSON.stringify(domain)}
                            style={{ ['--color' as string]: domain.color! }}
                        >
                            <div className="flex flex-row items-center gap-3">
                                {getLangProps({
                                    obj: domain,
                                    base: 'name',
                                    lang: locale
                                })}
                            </div>
                        </SelectItem>
                    ))}
                </SelectGroup>
            </SelectContent>
        </Select>
    )
}

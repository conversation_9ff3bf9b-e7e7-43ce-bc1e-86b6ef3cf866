import { useMutation } from '@tanstack/react-query';
import { ForgotPasswordFormData, ForgotPasswordResponse } from '../forgot-password.types';
import { useApiClient } from '../../../../contexts/AppContext';

export function useInitResetPassword() {
  const apiClient = useApiClient();

  return useMutation<ForgotPasswordResponse, Error, ForgotPasswordFormData>({
    mutationFn: async (data: ForgotPasswordFormData) => {
      const response = await apiClient.post('/api/auth/reset-password/init', data);
      return response.data;
    }
  });
}
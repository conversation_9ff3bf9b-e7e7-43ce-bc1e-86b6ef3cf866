import { Button } from '@dinobot/components-ui'
import { Label } from '@dinobot/components-ui'
import { RadioGroup, RadioGroupItem } from '@dinobot/components-ui'
import { Plus, Trash, Trash2 } from 'lucide-react'
import React, { useEffect, useId, useState } from 'react'
import QuestionContent from './question-content'
import {
    selectEvaluationParamsStore,
    useEvaluationParamsStore
} from '../../store/evaluation-params.store'
import QuestionContentResult from './question-content-result'
import { z } from 'zod'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { cn, isEmptyOrNull } from '@dinobot/utils'
import { Checkbox } from '@dinobot/components-ui'
import StatmentContentExo from './statment-content-exo'
import MediaDialog from './media-dialog'
import MediaTooltip from './media-tooltip'
import { ImagePreviewModal } from './dialogs/components/image-preview-modal'
import { VideoPreviewModal } from './dialogs/components/video-preview-modal'
import { PlayCircle } from 'lucide-react'
import { ControlExerciseMedia, ControlExercisePartialWithRelations, questionsTypeSchema } from '@dinobot/prisma'

type ExoContentProps = {
    exoNum: number
    exo: ControlExercisePartialWithRelations
}
type TypeEnum = z.infer<typeof questionsTypeSchema>

const ExoContent = ({ exoNum, exo }: ExoContentProps) => {
    const { t, i18n } = useTranslation('teacher/myClass/evaluation/next/exo')
    const dir = getLangDir(i18n.language)
    const [value, setValue] = useState<TypeEnum>(
        questionsTypeSchema.Enum.INDEPENDENT
    )
    const { exos, updateExo, removeExo } = useEvaluationParamsStore()
    const [exercisesMedia, setExercisesMedia] = useState<
        ControlExerciseMedia[]
    >((exo?.medias as ControlExerciseMedia[]) ?? [])
    const [exercisesMediaGlolbal, setExercisesMediaGlolbal] = useState<
        ControlExerciseMedia[]
    >((exo?.medias as ControlExerciseMedia[]) ?? [])

    // State for modals
    const [isPreviewOpen, setIsPreviewOpen] = useState(false)
    const [previewMedia, setPreviewMedia] = useState<{
        url: string
        name: string
        type: 'image' | 'video'
    } | null>(null)

    const hasStatement = selectEvaluationParamsStore.use.hasStatement()
    const setHasStatement = selectEvaluationParamsStore.use.setHasStatement()
    const setMediaCount = selectEvaluationParamsStore.use.setMediaCount()
    const mediaCount = selectEvaluationParamsStore.use.mediaCount()
    const [isEditeurOpen, setIsEditeurOpen] = useState(false)
    const radioIdD = useId()
    const radioIdInd = useId()
    const statement = useId()

    const handleOpenPreview = (
        url: string,
        name: string,
        type: 'image' | 'video'
    ) => {
        setPreviewMedia({ url, name, type })
        setIsPreviewOpen(true)
    }

    const handleClosePreview = () => {
        setIsPreviewOpen(false)
        setPreviewMedia(null)
    }

    const updateExoType = (type: string) => {
        setValue(type as TypeEnum)
        updateExo(exoNum, { ...exo, questionsType: type as TypeEnum })
    }
    useEffect(() => {
        updateExoType(value)
    }, [])
    useEffect(() => {
        setHasStatement(exoNum, exo.hasStatment!)
    }, [exo.medias])
    return (
        <div className="relative">
            <ImagePreviewModal
                isOpen={isPreviewOpen && previewMedia?.type === 'image'}
                onClose={handleClosePreview}
                imageUrl={previewMedia?.url}
                imageName={previewMedia?.name}
            />
            <VideoPreviewModal
                isOpen={isPreviewOpen && previewMedia?.type === 'video'}
                onClose={handleClosePreview}
                videoUrl={previewMedia?.url}
                videoName={previewMedia?.name}
            />
            <Button
                variant="outline"
                className={cn(
                    `text-dinoBotRed hover:text-dinoBotRed/80 absolute top-9 px-1 border-dinoBotRed bg-transparent`,
                    {
                        hidden:
                            exos.length <= 1 &&
                            exos.at(0)?.questions?.at(0)?.content
                    },
                    dir === 'rtl' ? 'left-3' : 'right-3'
                )}
                onClick={() => removeExo(exoNum)}
            >
                <Trash2 />
            </Button>
            <h3
                className={cn(
                    'font-bold text-dinoBotBlue',
                    dir === 'rtl' && 'text-end'
                )}
            >
                {t('title')} {exoNum + 1}
            </h3>
            <div
                className={cn(
                    'flex flex-col rounded-xl border border-dinoBotLightGray p-4'
                )}
            >
                <RadioGroup
                    value={value}
                    onValueChange={updateExoType}
                    className={cn(
                        'flex gap-4',
                        dir === 'rtl' && 'flex-row-reverse'
                    )}
                >
                    <div className="flex gap-2 items-center">
                        <RadioGroupItem
                            value={questionsTypeSchema.Enum.INDEPENDENT}
                            id={radioIdInd}
                        />
                        <Label htmlFor={radioIdInd}>{t('independent')}</Label>
                    </div>
                    <div className="flex gap-2 items-center">
                        <RadioGroupItem
                            value={questionsTypeSchema.Enum.RELIANT}
                            id={radioIdD}
                        />
                        <Label htmlFor={radioIdD}>{t('dependant')}</Label>
                    </div>
                </RadioGroup>
                <p
                    className={cn(
                        'text-dinoBotGray text-sm mt-2',
                        dir === 'rtl' && 'text-end'
                    )}
                >
                    {t('descript')}
                </p>
                {!isEmptyOrNull(exo.questions?.at(0)?.content) ? (
                    hasStatement[exoNum] && (
                        <div className="w-full grid grid-cols-1 grid-rows-1">
                            <StatmentContentExo
                                isEditeurOpen={isEditeurOpen}
                                setIsEditeurOpen={setIsEditeurOpen}
                                exoNum={exoNum}
                                title="question_title_result"
                            />
                            <div className="flex flex-wrap gap-2 flex-row p-2">
                                {exercisesMedia &&
                                    exercisesMedia.map((media, index) => {
                                        return (
                                            media.type === 'statement' && (
                                                <div
                                                    key={index}
                                                    className="relative rounded-md overflow-hidden bg-dinoBotRed border w-32 h-20 cursor-pointer"
                                                    onClick={() =>
                                                        handleOpenPreview(
                                                            media.fileUrl!,
                                                            media.fileName ||
                                                                'media',
                                                            media.fileType?.startsWith(
                                                                'image'
                                                            )
                                                                ? 'image'
                                                                : 'video'
                                                        )
                                                    }
                                                >
                                                    {media?.fileType?.startsWith(
                                                        'image'
                                                    ) ? (
                                                        <img
                                                            src={media.fileUrl!}
                                                            alt={
                                                                media.fileName ||
                                                                'media'
                                                            }
                                                            className="object-cover w-full h-full"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full bg-black flex items-center justify-center">
                                                            <video
                                                                src={
                                                                    media.fileUrl!
                                                                }
                                                                muted={true}
                                                                className="object-cover w-full h-full pointer-events-none"
                                                            />
                                                            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                                                                <PlayCircle className="text-white h-8 w-8" />
                                                            </div>
                                                        </div>
                                                    )}
                                                    <Button
                                                        onClick={e => {
                                                            e.stopPropagation() // Prevent modal from opening
                                                            setExercisesMedia(
                                                                exercisesMedia.filter(
                                                                    (_, i) =>
                                                                        i !==
                                                                        index
                                                                )
                                                            )
                                                            updateExo(exoNum, {
                                                                ...exo,
                                                                medias: exercisesMedia.filter(
                                                                    (_, i) =>
                                                                        i !==
                                                                        index
                                                                )
                                                            } as ControlExercisePartialWithRelations)
                                                            setMediaCount(
                                                                mediaCount - 1
                                                            )
                                                        }}
                                                        variant="ghost"
                                                        className="absolute bottom-1 left-1 p-0 flex justify-center items-center size-8 bg-dinoBotWhite border border-dinoBotRed"
                                                    >
                                                        <Trash className="cursor-pointer text-dinoBotRed" />
                                                    </Button>
                                                </div>
                                            )
                                        )
                                    })}
                                <div
                                    className={`flex gap-2  ${exercisesMedia?.length > 0 ? 'flex-col justify-between' : ''} `}
                                >
                                    <MediaDialog
                                        dialogType="statement"
                                        exoIndex={exoNum}
                                        setExercisesMedia={setExercisesMedia}
                                        exercisesMedia={exercisesMedia}
                                        exercise={exo}
                                    />
                                    <MediaTooltip />
                                </div>
                            </div>
                        </div>
                    )
                ) : (
                    <div className="grid grid-cols-[15px_1fr] grid-rows-[15px_1fr] gap-2 items-center w-full">
                        <Checkbox
                            id={statement}
                            checked={hasStatement[exoNum]}
                            onCheckedChange={check =>
                                setHasStatement(exoNum, check as boolean)
                            }
                            className={`data-[state=checked]:bg-dinoBotBlue border-dinoBotBlue peer/sta`}
                        />
                        <Label
                            htmlFor={statement}
                            className={cn(
                                'text-dinoBotGray text-sm',
                                dir === 'rtl' && 'text-end'
                            )}
                        >
                            {t('statement.title')}
                        </Label>
                        <StatmentContentExo
                            isEditeurOpen={isEditeurOpen}
                            setIsEditeurOpen={setIsEditeurOpen}
                            exoNum={exoNum}
                            title="question_title"
                        />
                    </div>
                )}
                {exo.questions?.map((question, i) =>
                    typeof question.content === 'string' ? (
                        <QuestionContentResult
                            key={i}
                            question={question}
                            exoIndex={exoNum}
                            quesNum={i}
                            isEditeurOpen={isEditeurOpen}
                            setIsEditeurOpen={setIsEditeurOpen}
                        />
                    ) : (
                        <QuestionContent
                            key={i}
                            exoIndex={exoNum}
                            quesNum={i}
                            canRemove={(exo?.questions ?? [])?.length >= 2}
                        />
                    )
                )}
                <div
                    className={cn(
                        'flex justify-start',
                        dir === 'rtl' && 'justify-end'
                    )}
                >
                    <Button
                        variant="link"
                        className={`text-dinoBotBlue hover:text-dinoBotVibrantBlue px-0 ${exo.questions && exo.questions.at(-1)?.content ? '' : 'hidden'} `}
                        onClick={() =>
                            updateExo(exoNum, {
                                ...exo,
                                questions: [...exo.questions!, {}]
                            })
                        }
                    >
                        <Plus />
                        {t('add')}
                    </Button>
                </div>
                <section className="flex flex-wrap gap-2 flex-row p-2">
                    {exercisesMediaGlolbal &&
                        exercisesMediaGlolbal.map((media, index) => {
                            if (media.type === 'global') {
                                return (
                                    <div
                                        key={index}
                                        className="relative rounded-md overflow-hidden bg-dinoBotRed border w-32 h-20 cursor-pointer"
                                        onClick={() =>
                                            handleOpenPreview(
                                                media.fileUrl!,
                                                media.fileName || 'media',
                                                media.fileType?.startsWith(
                                                    'image'
                                                )
                                                    ? 'image'
                                                    : 'video'
                                            )
                                        }
                                    >
                                        {media?.fileType?.startsWith(
                                            'image'
                                        ) ? (
                                            <img
                                                src={media.fileUrl!}
                                                alt={media.fileName || 'media'}
                                                className="object-cover w-full h-full"
                                            />
                                        ) : (
                                            <div className="w-full h-full bg-black flex items-center justify-center">
                                                <video
                                                    src={media.fileUrl!}
                                                    muted={true}
                                                    className="object-cover w-full h-full pointer-events-none"
                                                />
                                                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                                                    <PlayCircle className="text-white h-8 w-8" />
                                                </div>
                                            </div>
                                        )}
                                        <Button
                                            onClick={e => {
                                                e.stopPropagation() // Prevent modal from opening
                                                setExercisesMediaGlolbal(
                                                    exercisesMediaGlolbal.filter(
                                                        (_, i) => i !== index
                                                    )
                                                )
                                                updateExo(exoNum, {
                                                    ...exo,
                                                    medias: exercisesMediaGlolbal.filter(
                                                        (_, i) => i !== index
                                                    )
                                                } as ControlExercisePartialWithRelations)
                                                setMediaCount(mediaCount - 1)
                                            }}
                                            variant="ghost"
                                            className="absolute bottom-1 left-1 p-0 flex justify-center items-center size-8 bg-dinoBotWhite border border-dinoBotRed"
                                        >
                                            <Trash className="cursor-pointer text-dinoBotRed" />
                                        </Button>
                                    </div>
                                )
                            }
                        })}
                    <div
                        className={`flex gap-2  ${exercisesMediaGlolbal?.length > 0 ? 'flex-col justify-between' : ''}  ${exo.questions && exo.questions.at(-1)?.content ? '' : 'hidden'}`}
                    >
                        <MediaDialog
                            dialogType="global"
                            exoIndex={exoNum}
                            setExercisesMedia={setExercisesMediaGlolbal}
                            exercisesMedia={exercisesMediaGlolbal}
                            exercise={exo}
                        />
                        <MediaTooltip />
                    </div>
                </section>
            </div>
        </div>
    )
}

export default ExoContent

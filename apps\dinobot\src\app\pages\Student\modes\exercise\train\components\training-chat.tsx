
//import { ChatScrollAnchor } from '@/components/chat-scroll-anchor'
import React, { useEffect, useReducer, useRef, useState, useCallback, useMemo } from 'react'
// import { Message } from '@/lib/chat/actions'
import { toast } from 'sonner'
// import {
//     Exo,
//     ExoOutput,
//     ExoSortsAndFilters,
//     ExoSubject
// } from '@/lib/exams/actions'
import { TrainingChatPanel } from './training-chat-panel'
// import {
//     TrainingModeGeneratorInput,
//     TrainingModeGeneratorOutput
// } from '@/lib/training/actions'
import QuestionViewer from './exo-viewer'
import {
    Check,
    ChevronLeft,
    Clipboard,
    FileText,
    Printer,
    RotateCw,
    X
} from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import generatePDF, { Margin } from 'react-to-pdf'
import TrainingCortexDialog from './training-cortex-dialog'
import Cookies from 'js-cookie'
import { ChatRequestOptions } from 'ai'

import ExoFromDbMin from '../../../exercise/components/min-tabs/exo-from-db'
import ExoFromFileMin from '../../../exercise/components/min-tabs/exo-from-file'
import ExoFromExamMin from '../../../exercise/components/min-tabs/exo-from-exam/exo-from-exam'
import { useChat } from '@ai-sdk/react'
import CheckMessageTraning from './check-message-traning'
import { Domain } from '@dinobot/prisma'
import { cn, Session, TrainingModeGeneratorOutput } from '@dinobot/utils'
import { Chapter, ExoSubject, TrainingModeGeneratorInput } from '../../trainig.types'
import { Button, ChatList, DesmosUi, EmptyScreen, messageFileType, MiniPDFReader, Tooltip, TooltipContent, TooltipTrigger } from '@dinobot/components-ui'
import { selectUseAvatarStore, selectUseExoModeStore, useAccountStore, useExoModeStore } from '@dinobot/stores'
import { useExerciseData } from '../../hooks'
import { useTranslation } from 'react-i18next'
import { useCurrentViewStore, VIEWS } from '../../../../Chat/stores/current-view-store'
import { ExoInfoFromDb, ExoInfoFromExam, ExoInfoFromFile } from '@dinobot/stores/lib/exercise-mode-store/exercise-store'
import { useLocation, useNavigate } from 'react-router-dom'
import { useScrollAnchor } from '@dinobot/hooks'
import { useAppContext } from '../../../../../../contexts/AppContext'

export interface TrainChatProps extends React.ComponentProps<'div'> {
    id?: string
    session?: Session
}

// Définir le type d'état
interface TrainingChatState {
    chapters: Chapter[]
    subjects: ExoSubject[]
    domains: Domain[]
    pdf: any
    pdfIsOpen: boolean
    exoLoading: boolean
    imageFile: string
    fileExtension: string
    previousPath: string
    messagesFileMap: Map<number, messageFileType>
}

export function TrainChat({
    className,
    session
}: TrainChatProps) {
    const {config} = useAppContext()
    // État initial
    const initialState: TrainingChatState = {
        chapters: [],
        subjects: [],
        domains:[],
        pdf: null,
        pdfIsOpen: false,
        exoLoading: false,
        imageFile: '',
        fileExtension: '',
        previousPath: '',
        messagesFileMap: new Map<number, messageFileType>()
    }

    // Utiliser le format simplifié de useReducer
    const [state, dispatch] = useReducer(
        (state: TrainingChatState, change: Partial<TrainingChatState>) => ({
            ...state,
            ...change
        }),
        initialState
    )

    // Destructurer l'état pour faciliter l'accès
    const {
        chapters,
        subjects,
        pdf,
        pdfIsOpen,
        exoLoading,
        imageFile,
        fileExtension,
        previousPath,
        messagesFileMap
    } = state
    const examId = useExoModeStore(state => state.exoInfoFromExam.examId)
    const { currentView } = useCurrentViewStore()
    
    // Use hooks for API calls
    const { 
        useExerciseById, 
        useDomainsByLevel, 
        useChaptersByDomainAndLevel,
        useSubjectsByDomainAndLevel,
        generateExercises,
        regenerateExercises
    } = useExerciseData()
    
    const { user } = useAccountStore()
    const userLevelId = user?.userLevel?.id
    const topic = Number(Cookies.get('topicId'))
    
    // Get domains, chapters and subjects data
    const { data: domainsData } = useDomainsByLevel(userLevelId)
    const { data: chaptersData } = useChaptersByDomainAndLevel(
        topic, 
        userLevelId
    )
    const { data: subjectsData } = useSubjectsByDomainAndLevel(
        topic
    )
    
    const setKnowledgeBaseModeContent = selectUseAvatarStore.use.setKnowledgeBaseModeContent()
    const {i18n} = useTranslation()
    const navigate = useNavigate()
    const path = useLocation().pathname
    const locale = i18n.language
    const exo = selectUseExoModeStore.use.currentGExo()
    const level = selectUseExoModeStore.use.level()
    const setExo = selectUseExoModeStore.use.setCurrentGExo()
    const {t} = useTranslation(['app/mode'],{keyPrefix:'train'})
    const exosRef = useRef(null)
    const mode = selectUseExoModeStore.use.mode()
    
    // Get exercise data for PDF
    const { data: exerciseData } = useExerciseById(examId)

    const openAssignementPDF = useCallback(async () => {
        if (exerciseData && exerciseData.assignmentMedia) {
            dispatch({
                pdf: {
                    name: `${exerciseData.title} - énoncé`,
                    type: 'pdf',
                    data: `data:application/pdf;base64,${exerciseData.assignmentMedia}`
                },
                pdfIsOpen: false
            })
        }
    }, [exerciseData])

    useEffect(() => {
        if (examId && mode === 'FROM_EXAM') {
            ;(async () => {
                await openAssignementPDF()
            })()
        } else {
            dispatch({ pdf: null, pdfIsOpen: false })
        }
    }, [examId, mode, openAssignementPDF])
    const exoInfo =
        mode === 'FROM_FILE'
            ? selectUseExoModeStore.use.exoInfoFromFile()
            : mode === 'FROM_EXAM'
              ? selectUseExoModeStore.use.exoInfoFromExam()
              : selectUseExoModeStore.use.exoInfoFromDb()
    const {
        messages,
        input,
        handleInputChange,
        handleSubmit,
        status,
        setMessages,
        error
    } = useChat({
        initialMessages: [],
        api: `${config.apiUrl}/api/chat${locale && `?locale=${locale}`}`,
        body: {
            imageFile,
            fileExtension,
            exercise: exo
        },
        credentials: 'include',
    })
    const resetState = useExoModeStore(state => state.reset)
    
    // Memoize dispatch callbacks to prevent infinite re-renders
    const handlePdfClose = useCallback(() => {
        dispatch({ pdfIsOpen: false })
    }, [])
    
    const handlePdfOpen = useCallback(() => {
        dispatch({ pdfIsOpen: true })
    }, [])
    
    const handleSetImageFile = useCallback((value: string) => {
        dispatch({ imageFile: value })
    }, [])
    
    const handleSetFileExtension = useCallback((value: string) => {
        dispatch({ fileExtension: value })
    }, [])
    
    const reset = useCallback(() => {
        resetState()
        navigate(-1)
    }, [resetState, navigate])

    // Get domain data - using existing domain from session or store
    const domain = useMemo(() => {
        return user?.userLevel ? { id: topic, name: 'Domain' } : null
    }, [user?.userLevel, topic])
    // const [_, setNewChatId] = useLocalStorage('newChatId', id)
    const regenerateNewExo = useCallback(async () => {
        dispatch({ exoLoading: true })
        dispatch({ pdfIsOpen: false })
        
        try {
            const input: TrainingModeGeneratorInput = {
                difficulty: exoInfo.difficulty,
                numberOfExercises: exoInfo.exoNbr,
                numberOfQuestions: exoInfo.qstNbr,
                variation: exoInfo.variation,
                customPrompt: exoInfo.customPrompt,
                lastResult: `- ${exo.map(x => x.questionContent).join('/n - ')}`,
                level: level!,
                domain: domain!
            }
            
            const result = await regenerateExercises.mutateAsync(input)
            if (result) {
                setExo(result)
            }
        } catch (error) {
            toast.error(t('error'))
        } finally {
            dispatch({ exoLoading: false })
        }
    }, [exoInfo, exo, level, domain, regenerateExercises, setExo, t])

    const generateNewExo = useCallback(async () => {
        dispatch({ exoLoading: true })
        
        try {
            let input: TrainingModeGeneratorInput
            
            switch (mode) {
                case 'FROM_DB':
                    input = {
                        difficulty: exoInfo.difficulty,
                        numberOfExercises: exoInfo.exoNbr,
                        numberOfQuestions: exoInfo.qstNbr,
                        partIds: (exoInfo as ExoInfoFromDb).partIds,
                        variation: exoInfo.variation,
                        customPrompt: exoInfo.customPrompt,
                        level: level!,
                        domain: domain!
                    }
                    break
                case 'FROM_FILE':
                    input = {
                        difficulty: exoInfo.difficulty,
                        numberOfExercises: exoInfo.exoNbr,
                        numberOfQuestions: exoInfo.qstNbr,
                        file: (exoInfo as ExoInfoFromFile).exo,
                        solutionFile: (exoInfo as ExoInfoFromFile).solution,
                        variation: exoInfo.variation,
                        customPrompt: exoInfo.customPrompt,
                        level: level!,
                        domain: domain!
                    }
                    break
                case 'FROM_EXAM':
                    input = {
                        difficulty: exoInfo.difficulty,
                        numberOfExercises: exoInfo.exoNbr,
                        numberOfQuestions: exoInfo.qstNbr,
                        exoId: (exoInfo as ExoInfoFromExam).examId,
                        variation: exoInfo.variation,
                        customPrompt: exoInfo.customPrompt,
                        level: level!,
                        domain: domain!
                    }
                    break
                default:
                    navigate(-1)
                    return
            }

            const exercice = await generateExercises.mutateAsync(input)
            
            if (exercice) {
                setExo(exercice)
            }
        } catch (error) {
            toast.error(t('error'))
            navigate(-1)
        } finally {
            dispatch({ exoLoading: false, pdf: null, pdfIsOpen: false })
        }
    }, [mode, exoInfo, level, domain, generateExercises, setExo, t, navigate])

    // Track if this is the first visit to prevent regeneration on domain changes
    const [isFirstVisit, setIsFirstVisit] = useState(true)

    // Update state when data is loaded
    useEffect(() => {
        if (domainsData) {
            dispatch({ domains: domainsData as Domain[] })
        }
    }, [domainsData])
    
    useEffect(() => {
        if (chaptersData) {
            dispatch({ chapters: chaptersData })
        }
    }, [chaptersData])
    
    useEffect(() => {
        if (subjectsData) {
            dispatch({ subjects: subjectsData })
        }
    }, [subjectsData])

    useEffect(() => {
        ;(async () => {
            if (userLevelId && domain) {
                // Only generate new exercise on first visit
                if (isFirstVisit) {
                    await generateNewExo()
                    setIsFirstVisit(false)
                }
            }
            dispatch({ pdf: null, pdfIsOpen: false })
        })()
    }, [userLevelId, domain, isFirstVisit, generateNewExo])

    useEffect(() => {
        if (previousPath.includes('chat') && path === '/') {
            setMessages([])
            window.location.reload()
        }
        dispatch({ previousPath: path })
    }, [path, previousPath, setMessages])
    
    // Separate useEffect to update knowledge base with messages and exercise
    useEffect(() => {
        if (exo && exo.length > 0) {
            setKnowledgeBaseModeContent(
                `voicie l'exercice ${JSON.stringify(exo)}\n et le chat ${JSON.stringify(messages)}`
            )
        }
    }, [exo, messages, setKnowledgeBaseModeContent])

    const {
        messagesRef,
        scrollRef,
        visibilityRef,
        isAtBottom,
        scrollToBottom
    } = useScrollAnchor()

    const copyExo = useCallback(() => {
        let text = ''
        exo.forEach((x, index) => {
            text += `${index + 1}. ${x.questionContent}\n`
        })
        navigator.clipboard.writeText(text)
    }, [exo])

    const printExos = useReactToPrint({
        contentRef: exosRef
    })


    const downloadAsPdf = useCallback(async () => {
        await generatePDF(exosRef, {
            filename: 'dinobot-exercise.pdf',
            page: { margin: Margin.SMALL }
        })
    }, [])

    const GenerateMinTab = () => {
        switch (mode) {
            case 'FROM_DB':
                return (
                    <ExoFromDbMin
                        domains={state.domains}
                        chapters={chapters}
                        // getChapters={getChapters}
                        // getParts={getParts}
                        onGenerate={generateNewExo}
                    />
                )
            case 'FROM_FILE':
                return <ExoFromFileMin onGenerate={generateNewExo} />
            case 'FROM_EXAM':
                return (
                    <ExoFromExamMin
                        domains={state.domains}
                        // getExams={getExams}
                        subjects={subjects}
                        onGenerate={generateNewExo}
                    />
                )
        }
    }

    const handleSubmitWithFile = useCallback((
        e?: { preventDefault?: () => void },
        chatRequestOptions?: ChatRequestOptions
    ) => {
        e?.preventDefault?.()

        // Si un fichier est présent, assurez-vous qu'il est inclus dans la requête
        if (imageFile) {
            // useChat va automatiquement utiliser le body configuré ci-dessus
            handleSubmit(e, chatRequestOptions)

            // Réinitialiser le fichier après l'envoi
            dispatch({ imageFile: '', fileExtension: '' })
        } else {
            // Envoi normal sans fichier
            handleSubmit(e, chatRequestOptions)
        }
    }, [imageFile, handleSubmit])

    // Fonction pour ajouter un fichier à la Map
    const addFileData = useCallback((data: messageFileType) => {
        const newMap = new Map(messagesFileMap)
        newMap.set(messages.length, data)
        dispatch({ messagesFileMap: newMap })
    }, [messagesFileMap, messages.length])

    return (
        <div className="bg-white relative size-full flex flex-col md:flex-row justify-center items-center overflow-hidden ">
            <CheckMessageTraning
                messages={messages}
                setMessages={setMessages}
                session={session}
            />
            {(currentView === VIEWS.SPLIT_SCREEN ||
                currentView === VIEWS.EXERCISE) && (
                <div
                    className={cn(
                        'w-full pt-4 relative h-2/5 md:h-full md:py-3 lg:px-8 2xl:pl-16 2xl:pr-8 overflow-y-auto overflow-x-hidden',
                        currentView === VIEWS.SPLIT_SCREEN &&
                            'md:w-1/2 border-b md:border-r border-dinoBotLightGray',
                        currentView === VIEWS.EXERCISE && 'md:w-1/2'
                    )}
                >
                    <button
                        onClick={reset}
                        className="flex justify-center items-center top-5 lg:mt-4 lg:left-3 left-5 xl:left-12 absolute size-8 bg-dinoBotBlue hover:bg-dinoBotBlue/80 text-white rounded-full"
                    >
                        <ChevronLeft className="size-6 mr-0.5" />
                    </button>
                    <div className="size-full flex flex-col px-2 xl:px-8  sm:mt-8 lg:mt-0">
                        {!exoLoading ? (
                            <div className="flex flex-col gap-3 p-4 xl:px-8">
                                <div className="absolute top-[-9999px] ">
                                    <div ref={exosRef}>
                                        <ExoPrintView exo={exo} />
                                    </div>
                                </div>
                                <div className="p-4 bg-dinoBotDarkGray/5 rounded-md">
                                    {GenerateMinTab()}
                                </div>
                                <div className="w-full flex justify-between py-1 border-b border-dinoBotBlue">
                                    <div className="text-lg font-extrabold text-dinoBotBlue">
                                        {/* {t('exo')} */}
                                    </div>
                                    <div>
                                        <ExoOptions
                                            onCopy={copyExo}
                                            onGenerate={regenerateNewExo}
                                            onPrint={printExos}
                                            onDownload={downloadAsPdf}
                                        />
                                    </div>
                                </div>
                                <div className="w-full pl-2 list-disc mb-4">
                                    {exo &&
                                        exo?.length > 0 &&
                                        exo?.map((x, index) => (
                                            <div key={index}>
                                                <p className="font-bold">
                                                    {t('exo')} {index + 1}:
                                                </p>
                                                <div className="animate-fade-in-down">
                                                    <QuestionViewer
                                                        value={
                                                            x.questionContent
                                                        }
                                                    />
                                                    {x.desmosCode &&
                                                    x.desmosCode.expressions ? (
                                                        <DesmosUi
                                                            data={x.desmosCode}
                                                            className="w-96 h-80"
                                                        />
                                                    ) : (
                                                        ''
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </div>
                        ) : (
                            <ExoLoading />
                        )}
                        {mode === 'FROM_EXAM' && (
                            <ExamPdfViewer
                                show={pdfIsOpen}
                                pdf={pdf}
                                onClose={handlePdfClose}
                                onOpen={handlePdfOpen}
                            />
                        )}
                    </div>
                </div>
            )}
            {(currentView === VIEWS.SPLIT_SCREEN ||
                currentView === VIEWS.CHAT) && (
                <div
                    className={cn(
                        'relative group w-full h-3/5 md:h-full overflow-auto pl-0',
                        currentView === VIEWS.SPLIT_SCREEN &&
                            'md:w-1/2 border-l border-l-dinoBotLightGray',
                        currentView === VIEWS.CHAT && 'md:w-2/3'
                    )}
                    ref={scrollRef}
                >
                    <div
                        className={cn(
                            'pb-[200px] mt-4 pt-4 md:pt-2',
                            className
                        )}
                        ref={messagesRef}
                    >
                        <div
                            className={` transition-all duration-300 px-8 2xl:px-0`}
                        >
                            {messages.length ? (
                                <ChatList
                                        
                                    messages={messages}
                                    exercise={undefined}
                                    status={status}
                                    error={error}
                                    messagesFileMap={messagesFileMap}
                                />
                            ) : (
                                // <EmptyScreen session={session!} />
                                <></>
                            )}
                        </div>

                        <div className="h-px w-full" ref={visibilityRef} />
                    </div>
                    <div
                        className={cn(
                            'w-full fixed bottom-8',
                            currentView === VIEWS.SPLIT_SCREEN && 'md:w-[50vw]',
                            currentView === VIEWS.CHAT &&
                                'md:w-full left-0 right-0'
                        )}
                    >
                        {messages.length ? (
                            <></>
                        ) : (
                            <EmptyScreen session={session!} />
                        )}
                        <TrainingChatPanel
                            input={input}
                            setInput={handleInputChange}
                            imageFile={imageFile}
                            setImageFile={handleSetImageFile}
                            fileExtension={fileExtension}
                            setFileExtension={handleSetFileExtension}
                            isAtBottom={isAtBottom}
                            scrollToBottom={scrollToBottom}
                            session={session}
                            status={status}
                            addFileData={addFileData}
                            handleSubmit={handleSubmitWithFile}
                        />
                    </div>
                    <TrainingCortexDialog />
                </div>
            )}
        </div>
    )
}

interface ExoOptionsProps {
    onCopy?: () => void
    onGenerate?: () => void
    onPrint?: () => void
    onDownload?: () => void
}

function ExoOptions({ onCopy, onGenerate, onPrint }: ExoOptionsProps) {
    const [animGenerate, setAnimGenerate] = useState(false)
    const [animCopy, setAnimCopy] = useState(false)
    const [animDownload, setAnimDownload] = useState(false)
    const {t} = useTranslation(['app/mode'],{keyPrefix:'train'})

    useEffect(() => {
        if (animGenerate) setTimeout(() => setAnimGenerate(false), 1500)
        if (animCopy) setTimeout(() => setAnimCopy(false), 1500)
        if (animDownload) setTimeout(() => setAnimDownload(false), 1500)
    }, [animGenerate, animCopy, animDownload])

    return (
        <div className="flex gap-2">
            {/* <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="ghost" className="size-5 p-0 hover:bg-background">
            <Volume2 />
            <span className="sr-only">Lire L&apos;exercice</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent className="bg-dinoBotBlue">
          Lire l&apos;exercice
        </TooltipContent>
      </Tooltip> */}

            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={
                            onCopy
                                ? () => {
                                      setAnimCopy(true)
                                      onCopy()
                                  }
                                : undefined
                        }
                    >
                        {!animCopy ? (
                            <Clipboard />
                        ) : (
                            <Check className="animate-fade-in" />
                        )}
                        <span className="sr-only">{t('copyexo')}</span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('copyexo')}
                </TooltipContent>
            </Tooltip>

            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={
                            onGenerate
                                ? () => {
                                      setAnimGenerate(true)
                                      onGenerate()
                                  }
                                : undefined
                        }
                    >
                        <RotateCw
                            className={`${animGenerate ? 'animate-spin' : ''}`}
                        />
                        <span className="sr-only">{t('regexo')}</span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('regexo')}
                </TooltipContent>
            </Tooltip>

            {/* <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            className="size-5 p-0 hover:bg-background"
            onClick={onDownload ? () => {
              setAnimDownload(true)
              onDownload()
            }:undefined}
          >
            {!animDownload ? (
              <Download />
            ) : (
              <Check className="animate-fade-in" />
            )}
            <span className="sr-only">Télécharger L&apos;exercice</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent className="bg-dinoBotBlue">
          Télécharger l&apos;exercice
        </TooltipContent>
      </Tooltip> */}

            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={onPrint ?? undefined}
                    >
                        <Printer />
                        <span className="sr-only">{t('printexo')}</span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('printexo')}
                </TooltipContent>
            </Tooltip>
        </div>
    )
}

function ExoLoading() {
    const {t} = useTranslation(['app/mode'],{keyPrefix:'train'})

    return (
        <div className="flex flex-col gap-3 p-4 xl:p-10">
            <div className="w-full flex justify-between py-1 border-b-1 animate-pulse">
                <div className="w-16 h-3 rounded-3xl bg-slate-400 animate-pulse"></div>
                <div className="w-28 h-3 rounded-3xl bg-slate-400 animate-pulse"></div>
            </div>
            <ul className="w-full">
                {[1, 2, 3, 4, 5].map((x, index) => (
                    <li key={index}>
                        <div className="w-full h-4 rounded-3xl bg-slate-300 my-2 animate-pulse"></div>
                    </li>
                ))}
            </ul>
            <div className="w-full flex justify-center">
                <div className="text-sm text-slate-500 font-semibold animate-pulse">
                    {t('creation')}
                </div>
            </div>
        </div>
    )
}

interface ExoPrintViewProps {
    exo: TrainingModeGeneratorOutput[]
}

function ExoPrintView({ exo }: ExoPrintViewProps) {
    const {t,i18n} = useTranslation(['app/mode'],{keyPrefix:'train'})
    const dir = i18n.dir()
    return (
        <div dir={dir}>
            <div className="w-full flex gap-2 items-center justify-center py-3 bg-dinoBotVibrantBlue/5">
                <img src='/dinobot-logo-chat.svg' alt="logo" width={48} height={48} />
                <div className="text-2xl text-dinoBotBlue font-semibold">
                    DinoBot
                </div>
            </div>
            <div className="flex flex-col gap-3 px-10 py-2">
                <div className="w-full flex justify-between py-1  border-b border-dinoBotBlue">
                    <div className="text-lg font-extrabold text-dinoBotBlue">
                        {t('exo')}
                    </div>
                    <div></div>
                </div>
                <ul className="w-full pl-4 list-disc ml-4 ">
                    {exo &&
                        exo?.length > 0 &&
                        exo?.map((x, index) => (
                            <li key={index}>
                                <p className="font-bold">
                                    {t('question')} {index + 1}:
                                </p>
                                <div style={{ pageBreakInside: 'avoid' }}>
                                    <QuestionViewer value={x.questionContent} />
                                    {x.desmosCode &&
                                    x.desmosCode.expressions ? (
                                        <DesmosUi
                                            data={x.desmosCode}
                                            className="w-96 h-80"
                                            style={{ pageBreakInside: 'avoid' }}
                                        />
                                    ) : (
                                        ''
                                    )}
                                </div>
                            </li>
                        ))}
                </ul>
            </div>
        </div>
    )
}

interface PdfViewerProps {
    show: boolean
    pdf: any
    onClose: () => void
    onOpen: () => void
}

function ExamPdfViewer({ show, pdf, onClose, onOpen }: PdfViewerProps) {
    return pdf && show ? (
        <div className="size-full  sm:w-[500px] sm:h-[600px] p-1 fixed bottom-1/2 translate-y-1/2 2xl:left-10 z-50 bg-white custom-scroller border border-dinoBotGray overflow-hidden">
            <div className=" px-2 py-1 w-full flex justify-end items-center">
                <Button
                    variant="ghost"
                    className="p-0 size-7 rounded-full hover:animate-wiggle hover:text-dinoBotRed text-dinoBotRed "
                    onClick={onClose}
                >
                    <X className="size-5" />
                </Button>
            </div>
            <div className="size-full overflow-y-auto overflow-x-hidden  custom-scroller">
                <div className=" flex items-center shadow-md  flex-col justify-start">
                    <div className="overflow-auto">
                        <MiniPDFReader file={pdf} sideViewer={true} />
                    </div>
                </div>
            </div>
        </div>
    ) : pdf ? (
        <div
            className="size-12 z-50 fixed  bottom-1/2 translate-y-1/2 left-5 rounded-full bg-dinoBotLightBlue border border-dinoBotVibrantBlue flex justify-center items-center text-dinoBotVibrantBlue cursor-pointer"
            onClick={onOpen}
        >
            <div>
                <FileText />
            </div>
        </div>
    ) : null
}

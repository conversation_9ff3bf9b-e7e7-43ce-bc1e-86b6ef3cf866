import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { Radical, Save } from 'lucide-react'
import { toast } from 'sonner'
import { z } from 'zod'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'
import rehypemathjax from 'rehype-mathjax'

import useCortexStore from '../../store/control-cortex-store'
import { selectUseAvatarStore, selectUseCtrlModeStore } from '@dinobot/stores'
import {
    StudentSubmissionPartialWithRelations,
    StudentSubmissionWithPartialRelations,
    StudentSubmissionWithPartialRelationsSchema
} from '@dinobot/prisma'
import { PlannedEvaluationWithPartialRelationsSchema } from '@dinobot/prisma'
import { StudentAnswerPartialWithRelations } from '@dinobot/prisma'
import { MemoizedReactMarkdown } from '@dinobot/components-ui'
import {
    Too<PERSON><PERSON>,
    Toolt<PERSON>Content,
    TooltipTrigger,
    But<PERSON>
} from '@dinobot/components-ui'
import { IconEdit } from '@dinobot/components-ui'
import { cn, ControlFeedbackOutput } from '@dinobot/utils'

import Loading from '../../components/loading'
import QuillTextArea from '../../components/control-text-area'
import ControlCortexDialog from '../../components/control-cortex-dialog'
import PaginationExos from '../../components/pagination-exos'
import CommentProfEval from './comment-prof-eval'
import { useAuthApiClient } from '../../../../../../contexts/AppContext'

interface Answer {
    controlQuestionId: string
    content: string
}

function transformAnswers(answers: Answer[]): Record<string, string> {
    return (
        answers?.reduce(
            (acc, item) => {
                if (item.controlQuestionId && item.content) {
                    acc[item.controlQuestionId] = item.content
                }
                return acc
            },
            {} as Record<string, string>
        ) || {}
    )
}

function transformControlDataWithSubmission(
    data: z.infer<typeof StudentSubmissionWithPartialRelationsSchema>
): ControlFeedbackOutput | null {
    if (!data) {
        return null
    }

    const { answers = [], ...studentSubmission } = data
    const questionsAnswer = transformAnswers(answers as Answer[])

    const exercises: any =
        studentSubmission.control?.exercises?.map((exercise, index) => ({
            id: index,
            title: `Exercice: ${index + 1}`,
            score: 0,
            questions:
                exercise.questions?.map(question => ({
                    id: question.id,
                    questionContent: question.content,
                    answer: questionsAnswer[question.id as string],
                    feedback: question.solution,
                    desmosCode: question.desmosCode,
                    contentType: question.type
                })) || []
        })) || []

    return {
        score: 0,
        scoreExplanation: '',
        exercises
    }
}

interface ProgrammedAnswerControlProps {
    plannedEvaluation: z.infer<
        typeof PlannedEvaluationWithPartialRelationsSchema
    >
}

const ProgrammedAnswerControl = ({
    plannedEvaluation
}: ProgrammedAnswerControlProps) => {
    const { t, i18n } = useTranslation(['app/mode/controle/answerControl'])
    const setKnowledgeBaseModeContent =
        selectUseAvatarStore.use.setKnowledgeBaseModeContent()
    const dir = getLangDir(i18n.language)
    const { openCortexDialog, text, setid, initValue, setText } =
        useCortexStore()
    const controle = selectUseCtrlModeStore.use.controle()
    const setControle = selectUseCtrlModeStore.use.setControle()
    const isRunning = selectUseCtrlModeStore.use.isRunning()
    const setIsRunning = selectUseCtrlModeStore.use.setIsRunning()
    const isLoading = selectUseCtrlModeStore.use.isLoading()
    const setLoading = selectUseCtrlModeStore.use.setLoading()
    const pause = selectUseCtrlModeStore.use.pauseTime()
    const setHasSubmission = selectUseCtrlModeStore.use.setHasSubmission()
    const hasSubmission = selectUseCtrlModeStore.use.hasSubmission()
    const setTimeHidden = selectUseCtrlModeStore.use.setTimeHidden()
    const transformControleDataWithoutSubmission =
        selectUseCtrlModeStore.use.transformControleDataWithoutSubmission()
    const isNoLimit = selectUseCtrlModeStore.use.isNoLimit()
    const setIsNoLimit = selectUseCtrlModeStore.use.setIsNoLimit()
    const navigate = useNavigate()
    const { evaluationId } = useParams<{ evaluationId: string }>()
    const apiClient = useAuthApiClient()
    const [hiddenSubmit, setHiddenSubmit] = useState(true)
    const [indexAnswer, setIndexAnswer] = useState(0)
    const flag = useRef(true)
    const isTimerFinich = useRef(true)
    const { t: translate } = useTranslation(['app/mode/train'])
    const [localAnswers, setLocalAnswers] = useState<string[][]>([])

    function preventSubmit() {
        setHasSubmission(true)
        setHiddenSubmit(true)
        setIsRunning(false)
        setLoading(false)
        setTimeHidden(true)
    }
    function preventNoSubmit(timeLimit: number | null) {
        setHasSubmission(true)
        setHiddenSubmit(false)
        setIsRunning(false)
        setLoading(false)
        if (timeLimit) setTimeHidden(false)
    }

    useEffect(() => {
        ;(async () => {
            try {
                if (!apiClient || !evaluationId) return

                const submissionResponse = await apiClient.get(
                    `/api/evaluation-scheduler/submissions/${evaluationId}`
                )
                const submission = submissionResponse.data as StudentSubmissionWithPartialRelations
                const { availableDate, dueDate, control, timeLimit } =
                    plannedEvaluation ?? {}

                if (!availableDate) {
                    console.error('Erreur : availableDate est manquant !')
                    return
                }

                const now = Date.now()

                if (now < availableDate.getTime()) {
                    navigate('/')
                    setLoading(true)
                    setControle(null)
                    return
                }

                if (submission) {
                    preventSubmit()
                    setControle(transformControlDataWithSubmission(submission))
                    return
                } else {
                    setControle(transformControlDataWithSubmission(submission))
                    preventNoSubmit(timeLimit)
                }

                if (dueDate && dueDate.getTime() < now) {
                    preventSubmit()
                    setControle(
                        transformControleDataWithoutSubmission(
                            control!,
                            translate
                        )
                    )
                    return
                } else {
                    setControle(
                        transformControleDataWithoutSubmission(
                            control!,
                            translate
                        )
                    )
                    preventNoSubmit(timeLimit)
                }
            } catch (error) {
                console.error(
                    'Erreur lors de la récupération des soumissions :',
                    error
                )
            }
        })()

        if (flag.current) {
            return
        }
        const initialAnswers = Array.from(
            { length: controle?.exercises.length || 0 },
            () =>
                Array.from(
                    { length: controle?.exercises[0]?.questions.length || 0 },
                    () => ''
                )
        )
        initValue(initialAnswers)
        setLocalAnswers(initialAnswers)
        flag.current = false
    }, [apiClient, evaluationId, plannedEvaluation, navigate])

    useEffect(() => {
        if (controle)
            setKnowledgeBaseModeContent(
                'voicie le controle en json' + JSON.stringify(controle)
            )
    }, [controle])

    const handleLocalAnswerChange = (
        value: string,
        exoIndex: number,
        questionIndex: number
    ) => {
        setLocalAnswers(prev => {
            const newAnswers = [...prev]
            if (!newAnswers[exoIndex]) {
                newAnswers[exoIndex] = []
            }
            newAnswers[exoIndex][questionIndex] = value
            return newAnswers
        })
    }

    const handleCortex = (idanswer: number, idcontent: number) => {
        openCortexDialog()
        setid(idanswer, idcontent)
    }
    // const change = (
    //     e: ChangeEvent<HTMLTextAreaElement>,
    //     exo: number,
    //     question: number
    // ) => {
    //     setValueInput({ exo, question, answer: e.target.value })
    // }

    const handleUpdate = (exo: number, question: number) => {
        const newControle = controle?.exercises || []
        const questionContent = newControle[exo].questions[question]
        const saved = questionContent.saved
        newControle[exo].questions[question] = {
            ...questionContent,
            answer: text[exo][question],
            saved: !saved
        }
        setControle({ ...controle, exercises: newControle })
    }

    const isAllQuestionsSaved = () => {
        if (!controle) return false
        return controle.exercises.every(exercise =>
            exercise.questions.every(question => question.saved)
        )
    }

    const handleSubmit = useCallback(async () => {
        console.log('TEXT =>', text)
        console.log('Controle =>', controle)
        try {
            if (!controle) throw new Error(t('errorMessage'))
            else if (!isAllQuestionsSaved())
                throw new Error(t('saveAllResponsePlease'))
            setLoading(true)
            pause()
            setHiddenSubmit(true)
            setIsRunning(false)
            const { score, scoreExplanation, exercises } = controle

            // Transforme les réponses des étudiants
            const studentAnswers = exercises.map(exercise =>
                exercise.questions.map(question => ({
                    controlQuestionId: question.id,
                    score: 0, // Score par défaut
                    content: question.answer || '', // Réponse de l'étudiant
                    feedback: '' // Feedback vide par défaut
                }))
            )

            console.log('StudentAnswers =>', studentAnswers)

            // Crée l'objet de soumission de l'étudiant
            const studentSubmission: StudentSubmissionPartialWithRelations = {
                globalScore: score, // Score global
                globalFeedback: scoreExplanation, // Feedback global
                answers: studentAnswers as StudentAnswerPartialWithRelations[] // Réponses des étudiants
            }

            console.log('StudentSubmission =>', studentSubmission)
            const result = await apiClient.post(
                `/api/evaluation-scheduler/submissions/${evaluationId}`,
                studentSubmission
            )

            if (result) {
                setLoading(false)
                setIsRunning(false)
                toast.success(t('successMessage'))
            } else {
                toast.error(t('errorMessage'))
            }
            // setIsNoLimit(false)
        } catch (error) {
            const err = error as Error
            toast.error(err.message)
        }
        // setIsNoLimit(false)
    }, [
        controle,
        evaluationId,
        pause,
        setIsNoLimit,
        setIsRunning,
        setLoading,
        t,
        apiClient
    ])

    // verify fi the timer need to be reset
    const shouldResetTimer = () =>
        !isLoading && isRunning && isTimerFinich.current

    // Reset timer state
    const resetTimer = () => {
        isTimerFinich.current = false
    }

    const saveAllQuestions = () => {
        if (controle) {
            controle.exercises.forEach((exercise, exoIndex) => {
                exercise.questions.forEach((question, questionIndex) => {
                    if (!question.saved) {
                        handleUpdate(exoIndex, questionIndex)
                    }
                })
            })
        }
    }

    // Verify if the conditions for submission are met
    const shouldSubmit = () =>
        !isRunning && !isTimerFinich.current && !isLoading && !hasSubmission

    useEffect(() => {
        if (shouldResetTimer()) {
            resetTimer()
            return
        }
        if (shouldSubmit()) {
            saveAllQuestions()
            handleSubmit()
        }
    }, [isRunning, hasSubmission])

    return (
        <div className=" flex flex-col size-full items-center">
            <CommentProfEval
                isCorrected={
                    isRunning ||
                    isLoading ||
                    isNoLimit ||
                    !plannedEvaluation.isCorrected
                }
            />
            <div
                className={`size-4/5 mx-5 my-3 2xl:my-8 border-gray-300 border-2 rounded p-8 xl:overflow-y-auto relative ${hiddenSubmit ? 'hidden' : ''}`}
            >
                {isLoading ? (
                    <Loading />
                ) : (
                    <>
                        <div className="w-full flex justify-center">
                            <PaginationExos
                                index={indexAnswer}
                                setIndex={setIndexAnswer}
                            />
                        </div>
                        <div className="flex flex-col items-center">
                            <div className="flex flex-col w-full">
                                {controle?.exercises[indexAnswer].questions.map(
                                    (content, questionIndex) => (
                                        <div
                                            key={'div ' + content.id}
                                            className="flex flex-col w-full"
                                        >
                                            <MemoizedReactMarkdown
                                                remarkPlugins={[
                                                    remarkGfm,
                                                    remarkMath
                                                ]}
                                                rehypePlugins={[
                                                    rehypeRaw,
                                                    rehypemathjax
                                                ]}
                                                components={{
                                                    p({ children }) {
                                                        return (
                                                            <p
                                                                className={cn(
                                                                    'prose prose-red break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0',
                                                                    'prose-p:text-sm prose-p:mt-1',
                                                                    'mb-2 last:mb-0'
                                                                )}
                                                            >
                                                                {questionIndex +
                                                                    1}
                                                                . {children}
                                                            </p>
                                                        )
                                                    }
                                                }}
                                            >
                                                {content.questionContent}
                                            </MemoizedReactMarkdown>
                                            <div className="flex w-full gap-1 items-center">
                                                <textarea
                                                    className="hidden border-2 border-gray-300 rounded-lg w-11/12 p-2"
                                                    disabled={
                                                        shouldSubmit() ||
                                                        content.saved
                                                    }
                                                    placeholder={t(
                                                        'placeholder'
                                                    )}
                                                    onChange={e =>
                                                        setText(
                                                            e.target.value,
                                                            indexAnswer,
                                                            questionIndex
                                                        )
                                                    }
                                                ></textarea>
                                                <QuillTextArea
                                                    key={
                                                        controle?.exercises[
                                                            indexAnswer
                                                        ].id +
                                                        '' +
                                                        content.id
                                                    }
                                                    placeholder={t(
                                                        'placeholder'
                                                    )}
                                                    disable={
                                                        shouldSubmit() ||
                                                        content.saved!
                                                    }
                                                    answerid={indexAnswer}
                                                    contentid={questionIndex}
                                                    value={
                                                        localAnswers[
                                                            indexAnswer
                                                        ]?.[questionIndex] || ''
                                                    }
                                                    onChange={value => {
                                                        //console.log("onchange",value)
                                                        handleLocalAnswerChange(
                                                            value,
                                                            indexAnswer,
                                                            questionIndex
                                                        )
                                                        setText(
                                                            value,
                                                            indexAnswer,
                                                            questionIndex
                                                        )
                                                    }}
                                                    theme={'snow'}
                                                    className={`chat border-2 border-gray-300 rounded-lg w-11/12 p-2 ${shouldSubmit() || content.saved ? ` cursor-not-allowed placeholder:text-dinoBotRed/50 placeholder:font-bold` : ''} resize-none bg-transparent  focus-within:outline-none sm:text-sm ${dir === 'rtl' ? 'rtl' : ''}`}
                                                />
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            type="button"
                                                            //variant="outline"
                                                            size="icon"
                                                            disabled={
                                                                shouldSubmit() ||
                                                                content.saved
                                                            }
                                                            className={`${shouldSubmit() || content.saved ? 'cursor-not-allowed' : ''} z-10  size-8 rounded-full bg-background p-0 sm:right-24 text-dinoBotRed hover:text-white dark:hover:text-gray-50 hover:bg-dinoBotRed hover:border-dinoBotRed hover:border transition-all duration-500`}
                                                            onClick={() =>
                                                                handleCortex(
                                                                    indexAnswer,
                                                                    questionIndex
                                                                )
                                                            }
                                                        >
                                                            {/* <ImagePlus className="" /> */}
                                                            <Radical />
                                                            <span className="sr-only">
                                                                {t(
                                                                    'addMathFormula'
                                                                )}
                                                            </span>
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent className="bg-dinoBotRed">
                                                        {t('addMathFormula')}
                                                    </TooltipContent>
                                                </Tooltip>
                                            </div>

                                            <Button
                                                className="bg-transparent text-dinoBotVibrantBlue pl-1 shadow-none hover:bg-slate-300 border-none w-fit "
                                                onClick={() =>
                                                    handleUpdate(
                                                        indexAnswer,
                                                        questionIndex
                                                    )
                                                }
                                                disabled={
                                                    shouldSubmit() ||
                                                    hiddenSubmit
                                                }
                                            >
                                                {content.saved ? (
                                                    <>
                                                        <IconEdit />
                                                        {t('editAnswer')}
                                                    </>
                                                ) : (
                                                    <>
                                                        <Save size={16} />
                                                        {t('saveAnswer')}
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    )
                                )}
                            </div>
                        </div>
                    </>
                )}
            </div>
            <Button
                className={`w-fit px-8 rounded-xl bg-dinoBotVibrantBlue ${hiddenSubmit ? 'hidden' : ''} `}
                disabled={shouldSubmit()}
                onClick={handleSubmit}
            >
                {' '}
                {t('submitAnswer')}
            </Button>
            <ControlCortexDialog />
        </div>
    )
}

export default ProgrammedAnswerControl

import React from 'react'
import StudentSidebar from '../../../layouts/SideBars/student-sidebar'
import { Outlet } from 'react-router-dom'
import { UserType } from '@dinobot/utils'
import { withCheckRole } from '@dinobot/components-ui'

function MyCoursesLayout() {
    return (
        <div className="flex flex-row w-screen h-screen overflow-hidden bg-[#FAFAFA]">
            <div className="w-fit">
                <StudentSidebar />
            </div>
            <div className="grow flex flex-col relative">
                <Outlet />
            </div>
        </div>
    )
}

export default  withCheckRole([UserType.STUDENT])(MyCoursesLayout)

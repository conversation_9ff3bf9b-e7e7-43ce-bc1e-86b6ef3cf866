import React, { useEffect } from 'react';
import { Session } from '@dinobot/utils';
import Cookies from 'js-cookie';
import { useNavigate } from 'react-router-dom';
import { UserStatusSchema, UserTypeSchema } from '@dinobot/prisma';
import ForceLogout from '../auth/ForceLogout';

function withCheckRole<P extends object>(allowedRoles: string[]) {
  return (WrappedComponent: React.ComponentProps<P & any>) => {
    return function WithCheckRole(props: P) {
      const navigate = useNavigate();
      const session = JSON.parse(Cookies.get('session') || '{}') as Session;
      const user = session.user;

      useEffect(() => {
        // Redirect to login if no user in session
        if (!session?.user) {
          navigate('/login');
          return;
        }

        const user = session.user;
        if (user && user.status === UserStatusSchema.enum.PENDING_DELETION) {
          navigate('/account-deletion-requested');
        }
        // Handle pending deletion status
        if (user.status === UserStatusSchema.enum.PENDING_DELETION) {
          navigate('/account-deletion-requested');
          return;
        }

        // Check if user has required role
        if (!allowedRoles.includes(user.type || '')) {
          // Redirect based on user type
          switch (user.type) {
            case UserTypeSchema.enum.student:
              navigate('/');
              break;
            case UserTypeSchema.enum.teacher:
              navigate('/teacher/my-classes');
              break;
            case UserTypeSchema.enum.establishment:
              navigate('/manage-content/skills');
              break;
            default:
              navigate('/');
          }
        }
      }, [navigate, session.user]);

      // Early return if no user
      if (!session?.user) {
        return null;
      }
      if (session.user && !user) {
        return <ForceLogout />;
      }
      // Show nothing if user doesn't have required role
      if (!allowedRoles.includes(session.user.type || '')) {
        return null;
      }
      return <WrappedComponent {...props} />;
    };
  };
}

export default withCheckRole;

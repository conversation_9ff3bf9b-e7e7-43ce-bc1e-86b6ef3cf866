import { useState } from 'react'
import { Check, Download, Loader, RotateCcw } from 'lucide-react'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'
import { useAuthApiClient } from '../../../../../../../contexts/AppContext'
import ImageViewer from './image-viewer'
import VideoPlayer from './video-player'
import { ControlQuestionMedia } from '@dinobot/utils'

interface Props {
    file: ControlQuestionMedia
    onClick: () => void
}

function Asset({ file, onClick }: Props) {
    const { t } = useTranslation(['app/mode/controle/controlPreview'])
    const apiClient = useAuthApiClient()

    const [isDownloading, setIsDownloading] = useState<
        'progress' | 'done' | 'none' | 'error'
    >('none')

    async function handleDownload(key: string, customFilename?: string) {
        try {
            setIsDownloading('progress')
            const response = await apiClient.get(`/api/control-mode/media/${encodeURIComponent(key)}`, {
                responseType: 'blob'
            })
            const file = response.data
            if (!file) {
                throw new Error(t('failedToDownload'))
            }

            const blobUrl = URL.createObjectURL(file)
            const filename =
                customFilename || key.split('/').pop() || 'download'

            const a = document.createElement('a')
            a.href = blobUrl
            a.download = filename
            a.style.display = 'none'

            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)

            URL.revokeObjectURL(blobUrl)
            setIsDownloading('done')
        } catch (error: any) {
            setIsDownloading('error')
            console.error('Failed download error:', error)
            toast.error(error.message)
        } finally {
            setTimeout(() => {
                setIsDownloading('none')
            }, 2000)
        }
    }

    return (
        <div
            onClick={onClick}
            className={`group relative min-w-[200px] max-w-[200px]  p-1 rounded  cursor-pointer  hover:bg-dinoBotVibrantBlue/10 `}
        >
            <button
                onClick={e => {
                    e.stopPropagation()
                    handleDownload(file.fileUrl!)
                }}
                className="absolute top-1.5 right-1.5 p-1 z-50 bg-dinoBotWhite/40 group-hover:block hidden hover:bg-dinoBotWhite transition-colors duration-200 rounded"
            >
                <Download />
            </button>
            {isDownloading != 'none' ? (
                <div className="size-full bg-black/10 absolute top-0 left-0 flex justify-center items-center">
                    {isDownloading === 'progress' ? (
                        <Loader className="animate-spin transition-all duration-[1500] size-8 text-dinoBotVibrantBlue" />
                    ) : isDownloading === 'done' ? (
                        <div className="size-10 bg-dinoBotGreen rounded-full flex justify-center items-center text-dinoBotWhite">
                            <Check className="size-8" />
                        </div>
                    ) : isDownloading === 'error' ? (
                        <button
                            onClick={e => {
                                e.stopPropagation()
                                handleDownload(file.fileUrl!)
                            }}
                            className="size-10 bg-dinoBotRed hover:bg-dinoBotRed/80 rounded-full flex justify-center items-center text-dinoBotWhite"
                        >
                            <RotateCcw className="size-6" />
                        </button>
                    ) : null}
                </div>
            ) : null}
            {file.fileType?.includes('image') ? (
                <ImageViewer url={file.signedUrl!} alt={file.fileName!} />
            ) : file.fileType?.includes('video') ? (
                <VideoPlayer url={file.signedUrl!} />
            ) : null}
        </div>
    )
}

export default Asset

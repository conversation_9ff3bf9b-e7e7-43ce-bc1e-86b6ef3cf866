import React, { useState, useTransition } from 'react'
import { toast } from 'sonner'
import SubscriptionStatusDisplay from './SubStatus'
import { IconSpinner } from '@dinobot/components-ui'
import { useTranslation } from 'react-i18next'
import { useLocation } from 'react-router-dom'
import { useAuthApiClient } from '../../../../../contexts/AppContext'
import { useMutation, useQuery } from '@tanstack/react-query'
import { SubscriptionStatusType } from '@dinobot/prisma'

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
//const stripePromise = loadStripe('');

interface StripePaymentComponentProps {
    subStatus: SubscriptionStatusType // Adjust the type if necessary
    trialEnd?: Date
}

export default function StripePaymentComponent({
    subStatus,
    trialEnd
}: StripePaymentComponentProps) {
    /*   const options = {
    // passing the client secret obtained from the server
    clientSecret: '{{CLIENT_SECRET}}',
  }; */
    const {t} = useTranslation(['app/chat'], { keyPrefix: 'profile.paiement' })
    
    const [initiatedPayment, setInitiatedPayment] = useState(false)

    const [isPending, startTransition] = useTransition()
    const pathname = useLocation().pathname
    const authApiClient = useAuthApiClient()

    // Hook to check if user is subscribed
    const { data: isSubscribed, refetch: refetchSubscription } = useQuery({
        queryKey: ['userSubscription'],
        queryFn: async (): Promise<boolean> => {
            const response = await authApiClient.get('/api/stripe/check-subscription')
            return response.data.isSubscribed
        },
        staleTime: 30 * 1000, // 30 seconds
    })

    // Mutation to create checkout session
    const checkoutMutation = useMutation({
        mutationFn: async (returnUrl: string) => {
            const response = await authApiClient.post('/api/stripe/checkout', {
                returnUrl
            })
            return response.data.sessionUrl
        },
    })

    // Mutation to create customer portal session
    const portalMutation = useMutation({
        mutationFn: async (returnUrl: string) => {
            const response = await authApiClient.post('/api/stripe/portal', {
                returnUrl
            })
            return response.data.sessionUrl
        },
    })

    const handlePayment = async () => {
        setInitiatedPayment(true)
        
        try {
            await refetchSubscription()
            let sessionUrl: string | null = null
            
            if (!isSubscribed) {
                // User is not subscribed, create checkout session
                sessionUrl = await checkoutMutation.mutateAsync(pathname)
            } else {
                // User is subscribed, create portal session  
                sessionUrl = await portalMutation.mutateAsync(pathname)
            }
            
            startTransition(() => {
                if (sessionUrl) {
                    window.location.href = sessionUrl
                    setInitiatedPayment(false)
                } else {
                    toast.error(t('error'))
                    setInitiatedPayment(false)
                }
            })
        } catch (error) {
            setInitiatedPayment(false)
            toast.error(t('error'))
            console.error('Payment error:', error)
        }
    }

    return (
        /*     <Elements 
        stripe={stripePromise} 
        //options={options}
        >
      <CheckoutPage />
    </Elements> */
        <>
            <div className="text-2xl text-dinoBotBlue font-bold mb-10">
                {t('abone')}
            </div>

            <div className="text-base">{t('benef')}</div>

            <button
                className={`flex flex-row justify-center items-center my-6  h-10 w-full bg-transparent border border-dinoBotBlue rounded-2xl text-dinoBotBlue hover:bg-dinoBotBlue hover:text-white transition-all duration-300 ${!initiatedPayment ? '' : 'opacity-30'} animate-fade-in-down`}
                aria-disabled={initiatedPayment}
                disabled={initiatedPayment}
                onClick={() => handlePayment()}
            >
                {initiatedPayment ? <IconSpinner /> : t('access')}
            </button>

            <div className="flex flex-row gap-2">
                {t('status')}
                <SubscriptionStatusDisplay status={subStatus} />
            </div>
            {trialEnd && (
                <div className="flex flex-row gap-2">
                    {t('timeup')}
                    <span className="font-bold">
                        {trialEnd.toLocaleDateString('fr-FR')}
                    </span>
                </div>
            )}
        </>
    )
}
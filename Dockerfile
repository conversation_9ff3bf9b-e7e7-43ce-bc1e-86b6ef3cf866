# Production deployment with nx serve
FROM node:22-alpine

# Install pnpm and serve for static file serving
RUN npm install -g pnpm serve

# Set working directory
WORKDIR /app

# Copy package files for dependency caching
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY nx.json tsconfig.base.json tsconfig.json ./

# Copy package.json files from apps and libs
COPY apps/dinobot/package.json ./apps/dinobot/
COPY apps/dinobot-backend/package.json ./apps/dinobot-backend/
COPY libs/components-ui/package.json ./libs/components-ui/
COPY libs/hooks/package.json ./libs/hooks/
COPY libs/prisma/package.json ./libs/prisma/
COPY libs/stores/package.json ./libs/stores/
COPY libs/utils/package.json ./libs/utils/

# Install dependencies
RUN pnpm install --no-frozen-lockfile

# Copy source code
COPY apps/ ./apps/
COPY libs/ ./libs/

# Sync Nx workspace and generate Prisma client
RUN pnpm nx sync && pnpm prisma:generate

# Build applications for production
ENV NX_DAEMON=false
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV APP_API_URL=https://dinobot-gar-staging.ouiactive.com
RUN pnpm nx build dinobot-backend --configuration=production
RUN pnpm nx build dinobot --configuration=production

# Copy docker entrypoint script
COPY docker-entrypoint.sh ./
RUN chmod +x docker-entrypoint.sh

# Expose ports
EXPOSE 4100 3001

# Use docker-entrypoint.sh
ENTRYPOINT ["./docker-entrypoint.sh"]

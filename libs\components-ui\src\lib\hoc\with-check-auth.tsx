import React, { useEffect } from 'react'
import { Session } from '@dinobot/utils'
import ForceLogout from '../auth/ForceLogout'
import Cookies from 'js-cookie'
import { useNavigate } from 'react-router-dom'
import { UserStatusSchema } from '@dinobot/prisma'

function withCheckAuth<P extends object>() {
    return (WrappedComponent: React.ComponentProps<P & any>) => {
        const Component =  (props: P) => {
            const session = (JSON.parse(Cookies.get('session') || '{}')) as Session
            const navigate = useNavigate()
            const user = session.user
            useEffect(() => {
                if (!session?.user) {
                    navigate('/login')
                }
                if (user && user.status !== UserStatusSchema.Enum.PENDING_DELETION) {
                    navigate( '/')
                }
            }, [navigate, session?.user, user])

            // If session exists, but user is not in DB (i.e., deleted), force logout.
            if (session.user && !user) {
                return <ForceLogout />
            }
            return <WrappedComponent {...props} />
        }
        Component.displayName = 'WithCheckAuth'
        return Component
    }
}

export default withCheckAuth

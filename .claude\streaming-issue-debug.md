# Hono + Vercel AI SDK Streaming Issue Debug Log

## Problem Description
The AI chat streaming endpoint in the Hono backend waits for the entire response to complete before showing any content to the frontend, instead of streaming tokens in real-time as they're generated.

**Expected Behavior:** Tokens should appear progressively as the AI generates them  
**Actual Behavior:** The entire response appears at once after completion  

## Environment
- **Backend:** Hono.js with @hono/node-server
- **AI SDK:** Vercel AI SDK with OpenAI
- **Frontend:** React with `useChat` hook
- **Development:** Local environment (localhost:3001 backend, localhost:3100 frontend)

## Attempts Made

### 1. **Simplified the Handler**
- Started with complex `chatHandlerImpl` function with OCR, telemetry, database operations
- Progressively stripped down to minimal example
- **Result:** No change - still waits for completion

### 2. **Removed Telemetry Wrapper**
- Replaced `tracedStreamText()` with direct `aiStreamText()`
- **Result:** No change

### 3. **Removed onFinish Callback**
- Eliminated database save operations in `onFinish` callback
- **Result:** No change - rules out database blocking

### 4. **Disabled Logger Middleware**
- Temporarily commented out custom Pino logger middleware in `app.ts`
- **Result:** No change - rules out middleware buffering

### 5. **Direct Inline Handler**
- Changed from separate `chatHandlerImpl` function to inline `async c =>` handler
- **Result:** No change

### 6. **TextEncoderStream Fix** (Failed)
- Applied GitHub issue #4017 fix: `.pipeThrough(new TextEncoderStream())`
- **Result:** Broke stream protocol - "Failed to parse stream string. No separator found."

### 7. **toDataStreamResponse Approach**
- Switched from manual `stream()` + `toDataStream()` to `result.toDataStreamResponse()`
- Added explicit headers: "Transfer-Encoding": "chunked", "Connection": "keep-alive"
- **Result:** No change

## Current Minimal Test Case
```typescript
chatRoutes.post('/', async c => {
  const result = streamText({
    model: openai('gpt-4o'),
    prompt: 'Invent a new holiday and describe its traditions.',
  });

  c.header('X-Vercel-AI-Data-Stream', 'v1');
  c.header('Content-Type', 'text/plain; charset=utf-8');

  return stream(c, stream => stream.pipe(result.toDataStream()));
});
```

## Research Findings

### GitHub Issues Investigated
- **Issue #7058:** Hono Backend, Next.js Frontend streaming not working when deployed
  - Solution: Remove duplicate transfer-encoding headers
  - Our case: Local development, headers already minimal

- **Issue #4017:** createDataStream not working with Hono
  - Solution: TextEncoderStream (breaks AI SDK protocol in our case)

### Common Causes Found
1. **Proxy/Nginx buffering** - Not applicable (local development)
2. **Middleware buffering responses** - Tested by disabling logger
3. **Incorrect headers** - Using exact headers from documentation
4. **onFinish blocking** - Tested by removing entirely

## Working Reference
The old NextJS version uses:
```typescript
const result = streamText({...});
return result.toDataStreamResponse({
  getErrorMessage: error => randomErrorText
});
```

## Next Steps to Investigate
1. **Dependencies mismatch** - Compare package versions with working examples
2. **Environment variables** - Verify OPENAI_API_KEY and other config
3. **Network/infrastructure** - Test with different model providers
4. **Hono version** - Try different Hono versions
5. **AI SDK version** - Try different AI SDK versions

## Status
🔴 **UNRESOLVED** - Even minimal example from documentation doesn't stream properly

## Files Modified During Debug
- `/apps/dinobot-backend/src/lib/chat/routes.ts` - Multiple streaming approaches tested
- `/apps/dinobot-backend/src/app.ts` - Logger middleware temporarily disabled
- All changes have been reverted to original state
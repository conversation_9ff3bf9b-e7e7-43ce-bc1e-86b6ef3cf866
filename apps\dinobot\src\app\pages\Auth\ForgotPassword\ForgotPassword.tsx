import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import InitResetPasswordForm from './components/init-forgot-password-form';

const ForgotPassword: React.FC = () => {
  const navigate = useNavigate();

  // Check if user is already logged in (would use auth context/hook in real app)
  const isAuthenticated = false; // Replace with actual auth check

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  return (
    <main className="flex flex-col size-full">
      <InitResetPasswordForm />
    </main>
  );
};

export default ForgotPassword;

import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { serveStatic } from '@hono/node-server/serve-static'
import { serve } from '@hono/node-server'
import { showRoutes } from 'hono/dev'
import { logger } from '@/lib/logger'
import { initializeTelemetry } from '@/lib/telemetry/instrumentation'

// Initialize telemetry with Langfuse
const telemetrySdk = initializeTelemetry()

// Import module routes
import authRoutes from '@/lib/auth/routes'
import { chatRoutes } from '@/lib/chat'
import { voiceRoutes } from '@/lib/voice'
import { filesRoutes } from '@/lib/files'
import { webhooksRoutes } from '@/lib/webhooks'
import domainRoutes from '@/lib/domains/actions'
import levelRoutes from '@/lib/levels/actions'
import domainLevelRoutes from '@/lib/domain-level/actions'
import chapterRoutes from '@/lib/chapters/actions'
import examRoutes from '@/lib/exams/actions'
import trainingModeRoutes from '@/lib/training-mode/actions'
import trainingRoutes from '@/lib/training/actions'
import controlGeneratorRoutes from '@/lib/control-mode/generator/actions'
import { appreciationRoutes } from '@/lib/appreciation'
import { schoolCycleRoutes } from '@/lib/school-cycle'
import themeRoutes from '@/lib/theme/actions'
import { skillsRoutes } from '@/lib/skills'
import { praxeoRoutes } from '@/lib/praxeo'
import { evaluationSchedulerRoutes } from '@/lib/evaluation-scheduler'
import { partsRoutes } from '@/lib/parts'
import classRoutes from './lib/control-mode/services/class/actions'
import eventRoutes from '@/lib/control-mode/services/event/actions'
import accountRoutes from '@/lib/account/actions'
import featureRoutes from '@/lib/features/actions'
import requestsLevelDomainRoutes from '@/lib/requests-level-domain/action'
import controlModeRoutes from './lib/control-mode/actions'
// TODO: Import other routes when they're implemented
// import contentRoutes from '@/modules/content-management/content.routes'
const app = new Hono({
    strict: false
})

// Global middleware
app.use('*', cors({
    credentials: true,
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3100', 'http://localhost:3000', 'http://localhost:4100']
}))

// Serve static files from public directory
app.use('/*', serveStatic({ root: './public' }))

// Custom Pino logger middleware
app.use('*', async (c, next) => {
    const start = Date.now()

    logger.info({
        method: c.req.method,
        url: c.req.url,
        userAgent: c.req.header('user-agent')
    }, `--> ${c.req.method} ${c.req.url}`)

    await next()

    const duration = Date.now() - start
    logger.info({
        method: c.req.method,
        url: c.req.url,
        status: c.res.status,
        duration: `${duration}ms`
    }, `<-- ${c.req.method} ${c.req.url} ${c.res.status} ${duration}ms`)
})

// Health check
app.get('/health', c => {
    return c.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// Feature routes
app.route('/api/auth', authRoutes)
app.route('/api/chat', chatRoutes)
app.route('/api/voice', voiceRoutes)
app.route('/api/files', filesRoutes)
app.route('/api/webhooks', webhooksRoutes)
app.route('/api/domains', domainRoutes)
app.route('/api/levels', levelRoutes)
app.route('/api/domain-level', domainLevelRoutes)
app.route('/api/chapters', chapterRoutes)
app.route('/api/exams', examRoutes)
app.route('/api/training-mode', trainingModeRoutes)
app.route('/api/training', trainingRoutes)
app.route('/api/control-generator', controlGeneratorRoutes)
app.route('/api/control-mode', controlModeRoutes);
app.route('/api/appreciation', appreciationRoutes)
app.route('/api/school-cycles', schoolCycleRoutes)
app.route('/api/themes', themeRoutes)
app.route('/api/skills', skillsRoutes)
app.route('/api/praxeo', praxeoRoutes)
app.route('/api/evaluation-scheduler', evaluationSchedulerRoutes)
app.route('/api/parts', partsRoutes)
app.route('/api/classes', classRoutes)
app.route('/api/events', eventRoutes)
app.route('/api/account', accountRoutes)
app.route('/api/features', featureRoutes)
app.route('/api/requests-level-domain', requestsLevelDomainRoutes)

// TODO: Add other routes when they're implemented
// app.route('/api/content', contentRoutes)

// 404 handler
app.notFound(c => {
    return c.json({ error: 'Not found' }, 404)
})

// Error handler
app.onError((err, c) => {
    logger.error({
        error: {
            name: err.name,
            message: err.message,
            stack: err.stack
        },
        request: {
            method: c.req.method,
            url: c.req.url,

        }
    }, '=== Hono Error Handler ===')

    return c.json({ error: 'Internal server error' }, 500)
})

const port = process.env.PORT || 3001
logger.info({ port }, `Server is running on port ${port}`)

// Show all registered routes at startup
logger.info('=== Registered Routes ===')
showRoutes(app, {
    verbose: true,
    colorize: true,

})
logger.info('========================')

serve({
    fetch: app.fetch,
    port: Number(port)
})

// Graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('SIGTERM signal received: closing HTTP server')
    if (telemetrySdk) {
        await telemetrySdk.shutdown()
        logger.info('Telemetry SDK shut down')
    }
    process.exit(0)
})

process.on('SIGINT', async () => {
    logger.info('SIGINT signal received: closing HTTP server')
    if (telemetrySdk) {
        await telemetrySdk.shutdown()
        logger.info('Telemetry SDK shut down')
    }
    process.exit(0)
})

export default app

// apps/dinobot/src/app/pages/auth/forgot-password/components/init-reset-password-form.tsx
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useInitResetPassword } from '../hooks/useInitResetPassword';
import { ForgotPasswordFormData } from '../forgot-password.types';
import { Link } from 'react-router-dom';

// Spinner component to match the original
const IconSpinner: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div
      className={`h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent ${className || ''}`}
    />
  );
};

// SendResetEmail component to match the original button structure
const SendResetEmail: React.FC<{ isPending: boolean }> = ({ isPending }) => {
  const { t } = useTranslation(['app/auth']);

  return (
    <button
      type="submit"
      disabled={isPending}
      className="mt-4 flex h-10 w-full flex-row items-center justify-center rounded-md bg-dinoBotBlue px-4 text-sm font-medium text-white transition-colors hover:bg-dinoBotBlue/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
    >
      {isPending ? <IconSpinner /> : t('reset-password.submit')}
    </button>
  );
};

const InitResetPasswordForm: React.FC = () => {
  const [formData, setFormData] = useState<ForgotPasswordFormData>({
    email: '',
  });
  const [isResetInitiated, setIsResetInitiated] = useState(false);

  const { t } = useTranslation(['app/auth']);
  const initResetMutation = useInitResetPassword();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await initResetMutation.mutateAsync(formData);
      setIsResetInitiated(true);
    } catch {}
  };

  return (
    <div
      className="relative bg-white size-full flex flex-col gap-11 justify-evenly items-center overflow-auto custom-scroller pb-10"
      style={{ backgroundImage: "url('/bg-with-icons.webp')" }}
    >
      <img
        src={'/auth-bg-icons.webp'}
        alt="oui active"
        width={908}
        height={465}
        className="absolute hidden md:block  top-0 left-1/2 -translate-x-1/2"
      />
      <div className="mt-14">
        <img
          src={'/oui-active.svg'}
          alt="oui active"
          width={322}
          height={55}
          className="z-50"
        />
      </div>

      {!isResetInitiated ? (
        <form
          onSubmit={handleSubmit}
          className="relative flex flex-col items-center gap-4 space-y-3"
        >
          <div className=" w-[95%] flex-1 rounded-lg border  px-6 pb-4 pt-8 shadow-md  md:w-[450px]  z-50 bg-clip-padding  backdrop-blur-md backdrop-opacity-90 bg-[#f5f5f5]/80 saturate-100 backdrop-contrast-100">
            <h1 className="mb-3 text-3xl font-bold text-center text-slate-900 ">
              {t('reset-password.title')}
            </h1>
            <div className="w-full">
              <div>
                <label
                  className="block text-lg font-medium text-dinoBotBlue "
                  htmlFor="email"
                >
                  {t('reset-password.email.title')}
                  <span className="text-dinoBotRed font-semibold">*</span>
                </label>
                <div className="relative">
                  <input
                    className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[9px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950"
                    id="email"
                    type="email"
                    name="email"
                    placeholder={t('reset-password.email.placeholder')}
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
            </div>
            <div className="w-full flex justify-center">
              <SendResetEmail isPending={initResetMutation.isPending} />
            </div>
            <div className="flex justify-between">
              <Link
                to="/login"
                className="flex flex-row gap-1 justify-center items-center text-xs  text-dinoBotGray w-full mt-4"
              >
                <div className="text-dinoBotVibrantBlue underline">
                  {t('reset-password.connexion')}
                </div>
              </Link>
              <Link
                to="/signup"
                className="flex flex-row gap-1 justify-center items-center text-xs  text-dinoBotGray w-full mt-4"
              >
                <div className="text-dinoBotVibrantBlue underline">
                  {t('reset-password.incription')}
                </div>
              </Link>
            </div>
          </div>
          <img
            src={'/dinobot-all.svg'}
            alt="dinobot"
            width={260}
            height={280}
            className="absolute bottom-4 left-3/4 "
          />
          <img
            src={'/dinobot-tv.svg'}
            alt="dinobot"
            width={175}
            height={175}
            className="absolute bottom-10 right-[90%] "
          />
        </form>
      ) : (
        <div className="relative w-[450px] flex flex-col items-center gap-4 space-y-3">
          <div
            className="w-full flex-1 rounded-lg border drop-shadow-md px-6 pb-4 pt-8 shadow-sm dark:bg-zinc-950 z-50 bg-clip-padding  backdrop-blur-md backdrop-opacity-90 bg-[#f5f5f5]/80 saturate-100 backdrop-contrast-100"
            role="alert"
          >
            <div className="flex gap-4">
              <div className="py-1 ">
                <img
                  alt="DinoBot Logo"
                  loading="lazy"
                  width="383"
                  height="288"
                  decoding="async"
                  className="w-36 sm:w-48 mb-4"
                  src="/dinobot-logo.svg"
                  style={{ color: 'transparent' }}
                />
              </div>
              <div>
                <p className="text-lg font-bold text-dinoBotBlue mb-2">
                  {t('reset-password.mailsend')}
                </p>
                <p className="text-sm">{t('reset-password.mailsend-text')}</p>
              </div>
            </div>
            <Link
              className="w-full mt-4 flex flex-row justify-center gap-1 text-sm text-zinc-400 hover:underline"
              to="/login"
            >
              <p>{t('reset-password.return')}</p>
              <div className="font-semibold">{t('reset-password.cnx')}</div>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default InitResetPasswordForm;
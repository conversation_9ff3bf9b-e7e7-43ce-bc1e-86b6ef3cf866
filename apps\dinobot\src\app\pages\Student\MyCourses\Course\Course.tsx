import React from 'react'
import { useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import CourseDetails from './components/course-details'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { ClassWithPartialRelations } from '@dinobot/libs/prisma/src'

function Course() {
    const { courseId } = useParams<{ courseId: string }>()
    const apiClient = useAuthApiClient()

    const { data: course, isLoading, error } = useQuery({
        queryKey: ['course', courseId],
        queryFn: async (): Promise<ClassWithPartialRelations> => {
            if (!courseId) throw new Error('Course ID is required')
            const response = await apiClient.get(`/api/classes/student/${courseId}`)
            return response.data
        },
        enabled: !!courseId
    })

    if (isLoading) {
        return (
            <div className="size-full bg-dinoBotWhite p-6 border border-dinoBotLightGray flex items-center justify-center">
                <div>Loading course...</div>
            </div>
        )
    }

    if (error || !course) {
        return (
            <div className="size-full bg-dinoBotWhite p-6 border border-dinoBotLightGray flex items-center justify-center">
                <div>Error loading course</div>
            </div>
        )
    }

    return (
        <div className="size-full bg-dinoBotWhite p-6 border border-dinoBotLightGray">
            <h1 className="text-dinoBotDarkGray text-3xl font-bold m-10">
                {course.mainTeacher?.firstName} {course.mainTeacher?.lastName}
            </h1>
            <CourseDetails courseId={courseId!} />
        </div>
    )
}

export default Course
